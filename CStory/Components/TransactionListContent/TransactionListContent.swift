//
//  TransactionListContent.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/20.
//

import SwiftUI

/// 统一的交易列表内容组件
///
/// 使用新架构的TransactionRowVM数据源，为所有交易列表页面提供统一的显示组件。
/// 支持TransactionRecordTestView和CardDetailOverlayView等页面。
///
/// ## 使用示例
/// ```swift
/// // 方式1: 使用预先准备好的ViewModel（推荐）
/// let viewModel = TransactionListContentVM(
///     transactionDayGroups: dayGroups,
///     currencySymbol: "¥",
///     hasTransactions: true
/// )
/// TransactionListContent(viewModel: viewModel)
///
/// // 方式2: 直接传入数据参数
/// TransactionListContent(
///     transactionDayGroups: dayGroups,
///     currencySymbol: "¥",
///     hasTransactions: true
/// )
/// ```
struct TransactionListContent: View {

  // MARK: - ViewModel

  /// 交易列表内容视图模型
  @ObservedObject var viewModel: TransactionListContentVM

  // MARK: - 初始化

  /// 使用预先准备好的ViewModel初始化（推荐使用）
  init(viewModel: TransactionListContentVM) {
    self.viewModel = viewModel
  }

  /// 直接使用数据参数初始化
  init(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String,
    hasTransactions: Bool,
    emptyStateConfig: TransactionListContentVM.EmptyStateConfig = .default
  ) {
    self.viewModel = TransactionListContentVM(
      transactionDayGroups: transactionDayGroups,
      currencySymbol: currencySymbol,
      hasTransactions: hasTransactions,
      emptyStateConfig: emptyStateConfig
    )
  }

  // MARK: - 主体视图

  var body: some View {
    LazyVStack(spacing: 12) {
      if viewModel.hasTransactions {
        // 新架构：使用TransactionRowVM
        ForEach(viewModel.transactionDayGroups) { dayGroup in
          TransactionDayGroupView(
            dateText: dayGroup.dateText,
            income: dayGroup.dayIncome,
            expense: dayGroup.dayExpense,
            currencySymbol: viewModel.currencySymbol,
            content: {
              VStack(spacing: 0) {
                ForEach(Array(dayGroup.transactionRowVMs.enumerated()), id: \.offset) {
                  index, transactionRowVM in
                  TransactionRow(viewModel: transactionRowVM)
                    .padding(.horizontal, 16)
                    .padding(.bottom, index == dayGroup.transactionRowVMs.count - 1 ? 0 : 12)
                }
              }
            }
          )
        }

        // 底部提示
        Text("- 没有更多记录了 -")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.4))
          .padding(.vertical, 24)
      } else {
        // 空状态视图
        EmptyTransactionView(config: viewModel.emptyStateConfig)
      }
    }
  }
}

// MARK: - 子组件

/// 交易日期分组视图（新架构专用）
private struct TransactionDayGroupView<Content: View>: View {
  let dateText: String
  let income: Double
  let expense: Double
  let currencySymbol: String
  let content: () -> Content

  var body: some View {
    VStack(spacing: 12) {
      // 日期头部
      HStack {
        Text(dateText)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.4))
        Spacer()

        if income > 0 {
          HStack(spacing: 2) {
            Text("收")
              .font(.system(size: 10, weight: .medium))
              .foregroundColor(.white)
              .padding(.horizontal, 4)
              .padding(.vertical, 1)
              .background(Color.cAccentGreen)
              .cornerRadius(4)
            DisplayCurrencyView.size12(
              symbol: currencySymbol,
              amount: income
            )
            .color(.cAccentGreen)
          }
        }

        if expense > 0 {
          HStack(spacing: 2) {
            Text("支")
              .font(.system(size: 10, weight: .medium))
              .foregroundColor(.white)
              .padding(.horizontal, 4)
              .padding(.vertical, 1)
              .background(Color.cAccentRed)
              .cornerRadius(4)
            DisplayCurrencyView.size12(
              symbol: currencySymbol,
              amount: expense
            )
            .color(.cAccentRed)
          }
        }
      }
      .padding(.horizontal, 16)

      // 交易内容
      content()
    }
    .padding(.bottom, 8)
  }
}

/// 空交易记录视图
private struct EmptyTransactionView: View {
  let config: TransactionListContentVM.EmptyStateConfig

  var body: some View {
    VStack(spacing: 12) {
      if config.useSystemIcon {
        Image(systemName: config.icon)
          .font(.system(size: 32, weight: .light))
          .foregroundColor(.cBlack.opacity(0.3))
      } else {
        Image(config.icon)
          .resizable()
          .frame(width: 80, height: 80)
      }

      Text(config.text)
        .font(.system(size: 14, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.6))
    }
    .frame(height: 120)
    .frame(maxWidth: .infinity)
    .padding(.horizontal, 16)
  }
}
