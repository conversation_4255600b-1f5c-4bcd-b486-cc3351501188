//
//  TransactionListContentVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import SwiftUI
import Combine

/// 交易列表内容视图模型
///
/// 负责管理交易列表的显示数据，包括交易分组、货币符号、空状态配置等。
/// 所有的业务逻辑、数据格式化均由VM处理。
class TransactionListContentVM: ObservableObject {
  
  // MARK: - Output Properties (for the View to use)
  
  /// 交易日期分组数据
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM]
  
  /// 货币符号
  @Published var currencySymbol: String
  
  /// 是否有交易数据
  @Published var hasTransactions: Bool
  
  /// 空状态配置
  @Published var emptyStateConfig: EmptyStateConfig
  
  // MARK: - 空状态配置
  
  struct EmptyStateConfig {
    let icon: String
    let text: String
    let useSystemIcon: Bool
    
    static let `default` = EmptyStateConfig(
      icon: "doc.text",
      text: "暂无交易记录",
      useSystemIcon: true
    )
  }
  
  // MARK: - 初始化方法
  
  init(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String,
    hasTransactions: Bool,
    emptyStateConfig: EmptyStateConfig = .default
  ) {
    self.transactionDayGroups = transactionDayGroups
    self.currencySymbol = currencySymbol
    self.hasTransactions = hasTransactions
    self.emptyStateConfig = emptyStateConfig
  }
  
  // MARK: - 公共方法
  
  /// 更新交易数据
  func updateTransactionData(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String,
    hasTransactions: Bool
  ) {
    self.transactionDayGroups = transactionDayGroups
    self.currencySymbol = currencySymbol
    self.hasTransactions = hasTransactions
  }
  
  /// 更新空状态配置
  func updateEmptyStateConfig(_ config: EmptyStateConfig) {
    self.emptyStateConfig = config
  }
}
