//
//  DisplayCurrencyView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 货币金额显示组件
///
/// 专业的货币显示组件，支持多级样式配置和灵活的格式化选项。
/// 提供两种显示模式和丰富的自定义选项，适用于财务应用中的各种金额显示场景。
///
/// 该组件针对金融应用优化，支持精确的小数处理、多货币符号显示、
/// 以及分层的字体样式控制。通过工厂方法和链式调用提供便捷的使用方式。
///
/// ## 核心功能
/// - 多货币符号支持（¥、$、€等190+种货币）
/// - 分段式样式控制（符号、整数、小数独立设置）
/// - 简单模式和详细模式切换
/// - 数字格式化和千分位分隔
/// - 正负号和删除线支持
/// - 链式调用样式配置
///
/// ## 显示模式
/// - **详细模式**: 分段显示符号、整数、小数，支持不同字体样式
/// - **简单模式**: 单一文本显示，适合空间受限场景
///
/// ## 使用示例
/// ```swift
/// // 基本使用 - 详细模式
/// DisplayCurrencyView(
///   currencySymbol: "¥",
///   amount: 1234.56,
///   symbolSize: 16,
///   integerSize: 20,
///   decimalSize: 14
/// )
///
/// // 工厂方法创建
/// DisplayCurrencyView.size18(symbol: "$", amount: 999.99)
///   .color(.green)
///   .weight(integer: .bold)
///
/// // 简单模式
/// DisplayCurrencyView.size15(symbol: "€", amount: -200.50)
///   .simpleFormat()
///   .showingPlusSign(true)
/// ```
///
/// ## 样式尺寸预设
/// - `size32`: 主要金额展示（余额、净资产）
/// - `size20`: 编辑中的金额显示
/// - `size18`: 统计卡片、汇总数据
/// - `size16`: 收支统计数据
/// - `size15`: 交易列表主金额
/// - `size14`: 日期汇总、次要信息
/// - `size12`: 图表标签、迷你卡片
///
/// - Author: NZUE
/// - Since: 2025.4.11
/// - Note: 该组件针对金融数据显示优化，支持精确的浮点数处理
/// - SeeAlso: `CurrencyService`, `TransactionRow`
struct DisplayCurrencyView: View {
  // MARK: - Core Properties
  
  /// 货币符号字符串
  /// 
  /// 显示的货币符号，支持各国货币符号如¥、$、€、£等。
  /// 符号将根据symbolSize和symbolWeight进行样式化显示。
  var currencySymbol: String
  
  /// 要显示的金额数值
  /// 
  /// 支持正负数和小数，组件会自动处理符号显示和数字格式化。
  /// 负数会自动添加负号，可选择为正数添加加号。
  var amount: Double
  
  /// 是否显示小数部分
  /// 
  /// 控制小数部分的显示。当为false时，仅显示整数部分。
  /// 即使为true，整数金额也不会显示无意义的.00后缀。
  var showDecimals: Bool = true
  
  /// 是否使用简单显示模式
  /// 
  /// 简单模式将所有内容作为单一文本显示（如-¥200），
  /// 详细模式则分段显示符号、整数、小数并支持独立样式。
  var simpleMode: Bool = false
  
  /// 是否为正数显示加号
  /// 
  /// 当金额为正数时是否显示"+"号。负数始终显示"-"号。
  /// 在金融统计中常用于明确显示收入和支出。
  var showPlusSign: Bool = false
  
  /// 是否显示删除线效果
  /// 
  /// 为整个金额添加删除线样式，常用于表示已取消或原价格。
  var showStrikethrough: Bool = false

  // MARK: - Style Configuration
  
  /// 货币符号字体大小
  /// 
  /// 货币符号（¥、$等）的字体大小，单位为pt。
  /// 建议与整数大小保持合理比例以确保视觉平衡。
  var symbolSize: CGFloat
  
  /// 整数部分字体大小
  /// 
  /// 数字整数部分的字体大小，通常是最显眼的部分。
  /// 这是金额显示的主要视觉焦点。
  var integerSize: CGFloat
  
  /// 小数部分字体大小
  /// 
  /// 小数部分的字体大小，通常比整数部分略小。
  /// 仅在showDecimals为true且存在小数时生效。
  var decimalSize: CGFloat
  
  /// 货币符号字体粗细
  /// 
  /// 符号部分的字体权重，可选值包括.ultraLight到.black。
  /// 默认为.medium以保持适中的视觉权重。
  var symbolWeight: Font.Weight = .medium
  
  /// 整数部分字体粗细
  /// 
  /// 整数数字的字体权重，影响数字的视觉强度。
  /// 在重要金额显示中常使用.bold等较重权重。
  var integerWeight: Font.Weight = .medium
  
  /// 小数部分字体粗细
  /// 
  /// 小数部分的字体权重，通常比整数部分更轻。
  /// 用于创建视觉层次，突出整数部分的重要性。
  var decimalWeight: Font.Weight = .medium
  
  /// 主要文本颜色
  /// 
  /// 货币符号和整数部分的显示颜色。
  /// 默认使用深色以确保良好的可读性。
  var primaryColor: Color = Color.cBlack
  
  /// 次要文本颜色
  /// 
  /// 小数部分的显示颜色，通常比主色更浅。
  /// 默认为主色的60%透明度，创建合适的视觉层次。
  var secondaryColor: Color = Color.cBlack.opacity(0.6)

  // MARK: - Private Computed Properties
  
  /// 格式化后的整数部分字符串
  /// 
  /// 将金额的整数部分格式化为包含千分位分隔符的字符串。
  /// 使用绝对值处理，符号由视图层单独管理。
  /// 
  /// - Returns: 带千分位分隔符的整数字符串，如"1,234"
  private var formattedInteger: String {
    return NumberFormatService.shared.formatCurrencyInteger(amount)
  }

  /// 格式化后的小数部分字符串
  /// 
  /// 提取并格式化金额的小数部分，自动去除无意义的零。
  /// 只有在showDecimals为true且确实存在小数时才返回非空字符串。
  /// 
  /// - Returns: 带小数点的小数部分字符串（如".50"），整数则返回空字符串
  private var formattedDecimal: String {
    // 如果不显示小数，返回空字符串
    if !showDecimals {
      return ""
    }
    
    return NumberFormatService.shared.formatCurrencyDecimal(amount)
  }

  /// 简单模式下的完整格式化金额字符串
  /// 
  /// 在简单模式下使用的完整金额字符串，包含符号、货币符号和格式化数字。
  /// 自动处理正负号显示和小数精度控制。
  /// 
  /// - Returns: 完整的金额字符串，如"-¥1,234.56"或"+$500"
  private var simpleFormattedAmount: String {
    return NumberFormatService.shared.formatCurrency(
      amount,
      symbol: currencySymbol,
      showDecimals: showDecimals,
      showPlusSign: showPlusSign
    )
  }

  var body: some View {
    if simpleMode {
      // 简单模式：直接显示完整金额字符串（如 -¥200）
      Text(simpleFormattedAmount)
        .font(.system(size: integerSize, weight: integerWeight))
        .foregroundColor(primaryColor)
        .strikethrough(showStrikethrough)
    } else {
      // 分段模式：分别显示货币符号、整数部分和小数部分
      HStack(alignment: .firstTextBaseline, spacing: 2) {
        // 负号或加号（根据设置）
        if amount < 0 {
          Text("-")
            .font(.system(size: symbolSize, weight: symbolWeight))
            .foregroundColor(primaryColor)
            .strikethrough(showStrikethrough)
        } else if showPlusSign && amount > 0 {
          Text("+")
            .font(.system(size: symbolSize, weight: symbolWeight))
            .foregroundColor(primaryColor)
            .strikethrough(showStrikethrough)
        }

        // 货币符号
        Text(currencySymbol)
          .font(.system(size: symbolSize, weight: symbolWeight))
          .foregroundColor(primaryColor)
          .strikethrough(showStrikethrough)

        HStack(alignment: .firstTextBaseline, spacing: 0) {
          // 整数部分
          Text(formattedInteger)
            .font(.system(size: integerSize, weight: integerWeight))
            .foregroundColor(primaryColor)
            .strikethrough(showStrikethrough)

          // 小数部分（只有在有小数且显示小数设置为true时才显示）
          if !formattedDecimal.isEmpty {
            Text(formattedDecimal)
              .font(.system(size: decimalSize, weight: decimalWeight))
              .foregroundColor(secondaryColor)
              .strikethrough(showStrikethrough)
          }
        }
      }
    }
  }
}

// MARK: - Factory Methods

/// 工厂方法扩展
/// 
/// 提供预定义的尺寸配置工厂方法，简化常用场景的组件创建。
/// 每个方法都针对特定的使用场景进行了字体大小优化。
extension DisplayCurrencyView {
  
  /// 创建32号字体的货币显示组件
  /// 
  /// 适用于主要金额展示场景，如卡片余额、主页净资产等重要数据。
  /// 使用大号字体确保良好的视觉层次和可读性。
  /// 
  /// - Parameters:
  ///   - symbol: 货币符号（如"¥"、"$"、"€"）
  ///   - amount: 要显示的金额数值
  /// - Returns: 配置好32号字体的DisplayCurrencyView实例
  /// - Note: 符号22pt，整数32pt，小数18pt
  static func size32(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 22,
      integerSize: 32,
      decimalSize: 18
    )
  }

  /// 创建20号字体的货币显示组件
  /// 
  /// 适用于正在编辑的金额显示，提供清晰的输入反馈。
  /// 字体大小适中，既保持可读性又不会过于显眼。
  /// 
  /// - Parameters:
  ///   - symbol: 货币符号
  ///   - amount: 要显示的金额数值
  /// - Returns: 配置好20号字体的DisplayCurrencyView实例
  /// - Note: 符号18pt，整数20pt，小数16pt
  static func size20(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 18,
      integerSize: 20,
      decimalSize: 16
    )
  }

  /// 18号字体 - 统计卡片、汇总数据、金额编辑卡片
  static func size18(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 16,
      integerSize: 18,
      decimalSize: 14
    )
  }

  /// 16号字体 - 收支统计数据
  static func size16(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 14,
      integerSize: 16,
      decimalSize: 14
    )
  }

  /// 15号字体 - 交易列表主金额
  static func size15(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 13,
      integerSize: 15,
      decimalSize: 13
    )
  }

  /// 14号字体 - 日期汇总、次要信息
  static func size14(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 12,
      integerSize: 14,
      decimalSize: 12
    )
  }

  /// 12号字体 - 图表标签、次要金额、迷你卡片
  static func size12(symbol: String, amount: Double) -> DisplayCurrencyView {
    DisplayCurrencyView(
      currencySymbol: symbol,
      amount: amount,
      symbolSize: 10,
      integerSize: 12,
      decimalSize: 10
    )
  }
}

// MARK: - Fluent Interface

/// 链式调用扩展
/// 
/// 提供流畅的链式调用接口，支持在创建后进一步定制样式。
/// 所有方法都返回新的实例，保持值类型的不可变性。
extension DisplayCurrencyView {
  
  /// 设置文本颜色
  /// 
  /// 配置主要和次要文本的显示颜色。如果未指定次要色，
  /// 将自动使用主色的60%透明度作为次要色。
  /// 
  /// - Parameters:
  ///   - primary: 主要文本颜色（符号和整数）
  ///   - secondary: 次要文本颜色（小数），可选
  /// - Returns: 应用新颜色配置的DisplayCurrencyView实例
  func color(_ primary: Color, secondary: Color? = nil) -> DisplayCurrencyView {
    var view = self
    view.primaryColor = primary
    if let secondary = secondary {
      view.secondaryColor = secondary
    } else {
      view.secondaryColor = primary.opacity(0.6)
    }
    return view
  }

  /// 设置字体权重
  /// 
  /// 分别配置符号、整数、小数部分的字体粗细。
  /// 支持选择性配置，未指定的部分保持原有设置。
  /// 
  /// - Parameters:
  ///   - symbol: 符号部分字体权重，可选
  ///   - integer: 整数部分字体权重，可选  
  ///   - decimal: 小数部分字体权重，可选
  /// - Returns: 应用新字体权重的DisplayCurrencyView实例
  func weight(symbol: Font.Weight? = nil, integer: Font.Weight? = nil, decimal: Font.Weight? = nil)
    -> DisplayCurrencyView
  {
    var view = self
    if let symbol = symbol {
      view.symbolWeight = symbol
    }
    if let integer = integer {
      view.integerWeight = integer
    }
    if let decimal = decimal {
      view.decimalWeight = decimal
    }
    return view
  }

  func showingDecimals(_ show: Bool) -> DisplayCurrencyView {
    var view = self
    view.showDecimals = show
    return view
  }

  func simpleFormat() -> DisplayCurrencyView {
    var view = self
    view.simpleMode = true
    return view
  }

  func showingPlusSign(_ show: Bool) -> DisplayCurrencyView {
    var view = self
    view.showPlusSign = show
    return view
  }

  func strikethrough(_ show: Bool = true) -> DisplayCurrencyView {
    var view = self
    view.showStrikethrough = show
    return view
  }
}

#Preview {
  VStack(spacing: 20) {
    // 不同尺寸展示
    DisplayCurrencyView.size32(symbol: "¥", amount: 9527.32)

    DisplayCurrencyView.size20(symbol: "¥", amount: -1234.56)

    DisplayCurrencyView.size18(symbol: "¥", amount: 1000)

    DisplayCurrencyView.size16(symbol: "$", amount: 0.1)

    DisplayCurrencyView.size15(symbol: "$", amount: 0.11)

    DisplayCurrencyView.size14(symbol: "€", amount: 88.00)

    DisplayCurrencyView.size12(symbol: "€", amount: 3456.78)

    // 自定义字体粗细
    DisplayCurrencyView.size18(symbol: "¥", amount: 3456.78)
      .weight(symbol: .light, integer: .bold, decimal: .regular)

    // 简单模式显示
    DisplayCurrencyView.size15(symbol: "¥", amount: -200)
      .simpleFormat()

    // 简单模式显示小数
    DisplayCurrencyView.size15(symbol: "¥", amount: -200.5)
      .simpleFormat()

    // 自定义颜色的简单模式
    DisplayCurrencyView.size15(symbol: "¥", amount: 500)
      .simpleFormat()
      .weight(integer: .bold)
      .color(.cAccentGreen)
  }
  .padding()
}
