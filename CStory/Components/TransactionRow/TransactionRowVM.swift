//
//  TransactionRowVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

/// 交易行视图模型
///
/// 管理交易行组件的业务逻辑和数据处理，遵循MVVM架构模式。
/// 负责交易数据的格式化、状态计算和展示逻辑，为TransactionRow提供直接可用的数据。
///
/// 该类将TransactionModel数据转换为视图层可直接使用的格式，
/// 包括金额格式化、状态标签计算、时间显示处理等复杂业务逻辑。
///
/// ## 主要功能
/// - 交易数据格式化和计算
/// - 状态标签和颜色管理
/// - 金额显示逻辑（包括优惠和退款处理）
/// - 时间格式化和本地化
/// - 点击事件处理
///
/// ## 使用示例
/// ```swift
/// // 从TransactionModel创建
/// let viewModel = TransactionRowVM(
///   from: transactionModel,
///   showCardName: true,
///   showBalance: false
/// )
///
/// // 直接创建
/// let viewModel = TransactionRowVM(
///   icon: .emoji("🛍️"),
///   categoryName: "购物-日常用品",
///   formattedTime: "14:35",
///   displayAmount: -128.50,
///   amountColor: .black,
///   hasRefundTag: false,
///   hasDiscountTag: true
/// )
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.18
/// - Note: 该类支持动态数据更新，视图会自动响应属性变化
/// - SeeAlso: `TransactionRow`, `TransactionModel`
class TransactionRowVM: ObservableObject {

  // MARK: - Published Properties

  /// 交易分类图标
  ///
  /// 显示的分类图标，支持emoji和图片两种类型。
  /// 可为空，表示使用默认图标或不显示图标。
  @Published var icon: IconType?

  /// 交易分类名称
  ///
  /// 格式化后的分类显示名称，通常为"主分类-子分类"格式。
  /// 如"购物-日常用品"、"餐饮-午餐"等。
  @Published var categoryName: String

  /// 格式化后的时间字符串
  ///
  /// 根据交易时间智能格式化的显示文本。
  /// 如"14:35"、"昨天 18:20"、"7月15日"等。
  @Published var formattedTime: String

  /// 显示金额
  ///
  /// 经过优惠、退款等计算后的最终显示金额。
  /// 正数表示收入，负数表示支出。
  @Published var displayAmount: Double

  /// 金额显示颜色
  ///
  /// 根据交易类型和金额计算的显示颜色。
  /// 通常收入为绿色，支出为黑色，特殊状态有不同颜色。
  @Published var amountColor: Color

  /// 划线显示的原始金额
  ///
  /// 当存在优惠或退款时，显示的原始金额（带删除线）。
  /// 为空时表示无需显示原价。
  @Published var originalAmountForStrikethrough: Double?

  /// 货币符号
  ///
  /// 交易使用的货币符号，如"¥"、"$"、"€"等。
  /// 默认为人民币符号"¥"。
  @Published var currencySymbol: String

  /// 是否显示退款标签
  ///
  /// 控制退款状态标签的显示。当交易包含退款时为true。
  @Published var hasRefundTag: Bool

  /// 是否显示优惠标签
  ///
  /// 控制优惠状态标签的显示。当交易享受折扣时为true。
  @Published var hasDiscountTag: Bool

  /// 关联卡片名称
  ///
  /// 交易关联的银行卡或支付方式名称。
  /// 可选显示，用于区分不同的支付来源。
  @Published var relatedCardName: String?

  /// 余额显示文本
  ///
  /// 交易后的账户余额信息文本。
  /// 如"余额: ¥1024.50"、"信用卡欠款: ¥2500.00"等。
  @Published var balanceText: String?

  /// 是否显示加号
  ///
  /// 控制正数金额是否显示+号。在某些上下文中（如转账列表），不显示+号。
  @Published var shouldShowPlusSign: Bool

  // MARK: - Interaction

  /// 点击事件回调
  ///
  /// 当交易行被点击时执行的闭包，通常用于导航到详情页面。
  var onTap: (() -> Void)?

  // MARK: - Initialization

  init(
    icon: IconType? = nil,
    categoryName: String,
    formattedTime: String,
    displayAmount: Double,
    amountColor: Color,
    originalAmountForStrikethrough: Double? = nil,
    currencySymbol: String = "¥",
    hasRefundTag: Bool,
    hasDiscountTag: Bool,
    relatedCardName: String? = nil,
    balanceText: String? = nil,
    shouldShowPlusSign: Bool = false,
    onTap: (() -> Void)? = nil
  ) {
    self.icon = icon
    self.categoryName = categoryName
    self.formattedTime = formattedTime
    self.displayAmount = displayAmount
    self.amountColor = amountColor
    self.originalAmountForStrikethrough = originalAmountForStrikethrough
    self.currencySymbol = currencySymbol
    self.hasRefundTag = hasRefundTag
    self.hasDiscountTag = hasDiscountTag
    self.relatedCardName = relatedCardName
    self.balanceText = balanceText
    self.shouldShowPlusSign = shouldShowPlusSign
    self.onTap = onTap
  }

  // MARK: - 便利初始化方法

  /// 从TransactionModel创建TransactionRowVM
  convenience init(
    transaction: TransactionModel,
    relatedCard: CardModel? = nil,
    categoryInfo: (name: String, icon: IconType?) = ("未知类别", nil),
    displayContext: TransactionDisplayContext = .list,
    onTap: (() -> Void)? = nil
  ) {
    let formattedTime = Self.formatTransactionTime(transaction.transactionDate)
    let displayAmount = TransactionDisplayService.shared.calculateDisplayAmount(
      for: transaction, context: displayContext, relatedCard: relatedCard)
    let amountColor = TransactionDisplayService.shared.getAmountColor(
      for: transaction, context: displayContext, relatedCard: relatedCard)
    let originalAmount = Self.calculateOriginalAmount(
      for: transaction, context: displayContext, relatedCard: relatedCard)

    // 在卡片详情页面不显示卡片名称，但在列表页面显示
    let cardName = displayContext == .cardDetail ? nil : relatedCard?.name

    // 计算是否显示+号：转账交易在列表上下文中不显示+号
    let shouldShowPlus = TransactionDisplayService.shared.shouldShowPlusSign(
      for: transaction, context: displayContext, relatedCard: relatedCard)

    self.init(
      icon: categoryInfo.icon,
      categoryName: categoryInfo.name,
      formattedTime: formattedTime,
      displayAmount: displayAmount,
      amountColor: amountColor,
      originalAmountForStrikethrough: originalAmount,
      currencySymbol: transaction.symbol.isEmpty ? "¥" : transaction.symbol,
      hasRefundTag: (transaction.refundAmount ?? 0) > 0,
      hasDiscountTag: (transaction.discountAmount ?? 0) > 0,
      relatedCardName: cardName,
      shouldShowPlusSign: shouldShowPlus,
      onTap: onTap
    )
  }

  // MARK: - 静态方法（可复用的交易显示逻辑）

  /// 格式化交易时间
  static func formatTransactionTime(_ date: Date) -> String {
    return TransactionDisplayService.shared.formatTransactionTime(date, style: .smart)
  }

  /// 计算原始金额（用于显示划线价）
  static func calculateOriginalAmount(
    for transaction: TransactionModel, context: TransactionDisplayContext = .list,
    relatedCard: CardModel? = nil
  ) -> Double? {
    let hasDiscount = (transaction.discountAmount ?? 0) > 0
    let hasRefund = (transaction.refundAmount ?? 0) > 0

    if hasDiscount || hasRefund {
      // transactionAmount 存储的是原价，这就是要显示的划线价
      let originalAmount = transaction.transactionAmount

      // 根据交易类型返回正确的符号
      switch transaction.transactionType {
      case .expense:
        return -abs(originalAmount)  // 支出显示负数
      case .income, .refund:
        return abs(originalAmount)  // 收入和退款显示正数
      case .transfer:
        // 转账原始金额逻辑与显示金额保持一致
        switch context {
        case .cardDetail:
          // 卡片详情页：根据卡片角色显示正负号
          if let card = relatedCard {
            if transaction.fromCardId == card.id {
              return -abs(originalAmount)  // 转出方显示负数
            } else if transaction.toCardId == card.id {
              return abs(originalAmount)  // 转入方显示正数
            }
          }
          return abs(originalAmount)  // 默认显示正数
        case .list:
          return abs(originalAmount)  // 列表页显示正数
        case .detail:
          return abs(originalAmount)  // 详情页显示正数
        case .fromCard:
          return -abs(originalAmount)  // 转出方显示负数
        case .toCard:
          return abs(originalAmount)  // 转入方显示正数
        }
      case .createCard, .adjustCard:
        return originalAmount  // 系统交易保留原始符号
      }
    }
    return nil
  }

}
