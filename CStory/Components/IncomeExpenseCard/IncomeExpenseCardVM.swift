//
//  IncomeExpenseCardVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Foundation

/// 收入支出卡片视图模型
class IncomeExpenseCardVM: ObservableObject {
  
  // MARK: - Published Properties
  
  @Published var income: Double
  @Published var expense: Double
  @Published var currencySymbol: String
  
  // MARK: - Initializer
  
  init(income: Double = 0.0, expense: Double = 0.0, currencySymbol: String = "¥") {
    self.income = income
    self.expense = expense
    self.currencySymbol = currencySymbol
  }
}