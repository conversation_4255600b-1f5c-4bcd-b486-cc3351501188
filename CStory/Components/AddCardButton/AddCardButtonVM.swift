//
//  AddCardButtonVM.swift
//  CStory
//
//  Created by <PERSON> on 2025/7/22.
//

import SwiftUI

/// 添加卡片按钮的视图模型
///
/// 管理添加卡片按钮的业务逻辑、状态和样式配置。
/// 支持多种按钮样式，包括标准样式和最小化样式。
final class AddCardButtonVM: ObservableObject {
  
  // MARK: - 公共属性
  
  /// 按钮样式
  let style: AddCardButtonStyle
  /// 导航动作
  let action: () -> Void
  
  // MARK: - 初始化
  
  /// 初始化添加卡片按钮视图模型
  /// - Parameters:
  ///   - style: 按钮样式
  ///   - action: 点击动作
  init(style: AddCardButtonStyle, action: @escaping () -> Void) {
    self.style = style
    self.action = action
  }
  
  // MARK: - 工厂方法
  
  /// 创建标准样式的添加卡片按钮
  /// - Parameter action: 点击动作
  /// - Returns: 配置好的视图模型
  static func standard(action: @escaping () -> Void) -> AddCardButtonVM {
    return AddCardButtonVM(style: .standard, action: action)
  }
  
  /// 创建最小化样式的添加卡片按钮
  /// - Parameter action: 点击动作
  /// - Returns: 配置好的视图模型
  static func minimal(action: @escaping () -> Void) -> AddCardButtonVM {
    return AddCardButtonVM(style: .minimal, action: action)
  }
}

// MARK: - 添加卡片按钮样式

/// 添加卡片按钮样式枚举
enum AddCardButtonStyle {
  /// 标准样式：显示图标和文本，适用于列表中
  case standard
  /// 最小化样式：仅显示加号图标，适用于卡片网格中
  case minimal
  
  // MARK: - 样式配置
  
  /// 按钮宽度
  var width: CGFloat? {
    switch self {
    case .standard:
      return nil // 使用 maxWidth: .infinity
    case .minimal:
      return 100
    }
  }
  
  /// 按钮高度
  var height: CGFloat {
    switch self {
    case .standard:
      return 48 // 与其他按钮保持一致
    case .minimal:
      return 60 // 与卡片高度保持一致
    }
  }
  
  /// 圆角半径
  var cornerRadius: CGFloat {
    switch self {
    case .standard:
      return 16
    case .minimal:
      return 14
    }
  }
  
  /// 背景颜色
  var backgroundColor: Color {
    switch self {
    case .standard:
      return Color.cWhite
    case .minimal:
      return Color.clear
    }
  }
  
  /// 前景色
  var foregroundColor: Color {
    return Color.cBlack.opacity(0.4)
  }
  
  /// 边框样式
  var strokeStyle: StrokeStyle {
    return StrokeStyle(lineWidth: 1, dash: [4])
  }
  
  /// 是否显示图标
  var showIcon: Bool {
    return true
  }
  
  /// 图标名称
  var iconName: String {
    switch self {
    case .standard:
      return "cardPlus_icon"
    case .minimal:
      return "plus"
    }
  }
  
  /// 图标大小
  var iconSize: CGFloat {
    switch self {
    case .standard:
      return 21
    case .minimal:
      return 20
    }
  }
  
  /// 是否显示文本
  var showText: Bool {
    switch self {
    case .standard:
      return true
    case .minimal:
      return false
    }
  }
  
  /// 按钮文本
  var text: String {
    return "添加卡片"
  }
  
  /// 字体大小
  var fontSize: CGFloat {
    return 15
  }
  
  /// 字体权重
  var fontWeight: Font.Weight {
    return .medium
  }
  
  /// 内容间距
  var spacing: CGFloat {
    return 8
  }
  
  /// 垂直内边距
  var verticalPadding: CGFloat {
    switch self {
    case .standard:
      return 12
    case .minimal:
      return 0
    }
  }
}