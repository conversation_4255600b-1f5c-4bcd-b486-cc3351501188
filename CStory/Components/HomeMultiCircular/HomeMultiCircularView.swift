//
//  HomeMultiCircularView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import SwiftUI

/// 主页多层圆环进度视图组件
///
/// 基于MVVM架构的多圆环进度显示组件，类似Apple Health的多圆环样式。
/// 支持最多三个同心圆环展示不同的进度，每个圆环可以独立配置颜色、进度和线宽。
///
/// ## 设计特点
/// - 遵循MVVM架构模式，业务逻辑与UI分离
/// - 同心圆环设计，内外层次分明
/// - 支持平滑动画过渡效果
/// - 自动计算圆环大小和间距
/// - 背景圆环提供视觉参考
///
/// ## 使用场景
/// - 资产统计展示（总资产、负债、净资产）
/// - 收支统计展示（收入、支出、余额）
/// - 多维度数据对比
/// - 统计信息可视化
///
/// ## 使用示例
/// ```swift
/// // 使用预配置的ViewModel
/// let viewModel = HomeMultiCircularVM.assetStatistics(
///   totalAsset: 0.8,
///   totalLiability: 0.3,
///   netAsset: 0.5
/// )
/// HomeMultiCircularView(viewModel: viewModel)
///
/// // 使用自定义ViewModel
/// let customVM = HomeMultiCircularVM(
///   rings: [
///     RingData(progress: 0.75, color: .blue, title: "数据1"),
///     RingData(progress: 0.60, color: .green, title: "数据2"),
///     RingData(progress: 0.40, color: .orange, title: "数据3")
///   ]
/// )
/// HomeMultiCircularView(viewModel: customVM)
/// ```
struct HomeMultiCircularView: View {
  
  // MARK: - Properties
  
  /// 主页多圆环进度视图模型
  @ObservedObject var viewModel: HomeMultiCircularVM
  
  // MARK: - Initialization
  
  /// 使用ViewModel初始化
  /// - Parameter viewModel: 主页多圆环进度视图模型
  init(viewModel: HomeMultiCircularVM) {
    self.viewModel = viewModel
  }
  
  // MARK: - Body
  
  var body: some View {
    ZStack {
      ForEach(Array(viewModel.rings.enumerated()), id: \.offset) { index, ring in
        let ringSize = viewModel.ringSize(at: index)
        
        // 背景圆环
        Circle()
          .stroke(ring.color.opacity(0.12), lineWidth: ring.lineWidth)
          .frame(width: ringSize, height: ringSize)
        
        // 进度圆环
        Circle()
          .trim(from: 0.0, to: ring.progress)
          .stroke(
            ring.color,
            style: StrokeStyle(
              lineWidth: ring.lineWidth,
              lineCap: .round
            )
          )
          .frame(width: ringSize, height: ringSize)
          .rotationEffect(.degrees(-90))  // 从顶部开始
          .animation(
            viewModel.animation(for: index),
            value: ring.progress
          )
      }
    }
    .frame(width: viewModel.size, height: viewModel.size)
  }
}

// MARK: - 便利初始化器

extension HomeMultiCircularView {
  
  /// 使用默认配置创建多圆环进度视图
  /// - Parameters:
  ///   - outerProgress: 外圈进度，默认0.75
  ///   - middleProgress: 中圈进度，默认0.6
  ///   - innerProgress: 内圈进度，默认0.4
  ///   - size: 整体大小，默认90
  init(
    outerProgress: Double = 0.75,
    middleProgress: Double = 0.6,
    innerProgress: Double = 0.4,
    size: CGFloat = 90
  ) {
    let viewModel = HomeMultiCircularVM(
      outerProgress: outerProgress,
      middleProgress: middleProgress,
      innerProgress: innerProgress,
      size: size
    )
    self.viewModel = viewModel
  }
  
  /// 创建资产统计样式的多圆环进度视图
  /// - Parameters:
  ///   - totalAsset: 总资产进度
  ///   - totalLiability: 总负债进度
  ///   - netAsset: 净资产进度
  ///   - size: 整体大小，默认90
  static func assetStatistics(
    totalAsset: Double,
    totalLiability: Double,
    netAsset: Double,
    size: CGFloat = 90
  ) -> HomeMultiCircularView {
    let viewModel = HomeMultiCircularVM.assetStatistics(
      totalAsset: totalAsset,
      totalLiability: totalLiability,
      netAsset: netAsset,
      size: size
    )
    return HomeMultiCircularView(viewModel: viewModel)
  }
  
  /// 创建收支统计样式的多圆环进度视图
  /// - Parameters:
  ///   - income: 收入进度
  ///   - expense: 支出进度
  ///   - balance: 余额进度
  ///   - size: 整体大小，默认90
  static func incomeExpenseStatistics(
    income: Double,
    expense: Double,
    balance: Double,
    size: CGFloat = 90
  ) -> HomeMultiCircularView {
    let viewModel = HomeMultiCircularVM.incomeExpenseStatistics(
      income: income,
      expense: expense,
      balance: balance,
      size: size
    )
    return HomeMultiCircularView(viewModel: viewModel)
  }
}

// MARK: - 预览

#Preview {
  ScrollView {
    VStack(spacing: 40) {
      
      // MARK: 基本样式展示
      VStack(alignment: .leading, spacing: 20) {
        Text("基本样式")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 30) {
          VStack(spacing: 10) {
            Text("默认配置")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView()
          }
          
          VStack(spacing: 10) {
            Text("大尺寸")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 0.8,
              middleProgress: 0.6,
              innerProgress: 0.4,
              size: 120
            )
          }
          
          VStack(spacing: 10) {
            Text("小尺寸")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 0.9,
              middleProgress: 0.7,
              innerProgress: 0.5,
              size: 60
            )
          }
        }
      }
      
      Divider()
      
      // MARK: 统计样式展示
      VStack(alignment: .leading, spacing: 20) {
        Text("统计样式")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 30) {
          VStack(spacing: 10) {
            Text("资产统计")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView.assetStatistics(
              totalAsset: 0.85,
              totalLiability: 0.3,
              netAsset: 0.55
            )
          }
          
          VStack(spacing: 10) {
            Text("收支统计")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView.incomeExpenseStatistics(
              income: 0.75,
              expense: 0.6,
              balance: 0.4
            )
          }
          
          VStack(spacing: 10) {
            Text("极值展示")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView.assetStatistics(
              totalAsset: 1.0,
              totalLiability: 0.95,
              netAsset: 0.05
            )
          }
        }
      }
      
      Divider()
      
      // MARK: 自定义ViewModel展示
      VStack(alignment: .leading, spacing: 20) {
        Text("自定义ViewModel")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 30) {
          VStack(spacing: 10) {
            Text("彩色主题")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              viewModel: HomeMultiCircularVM(
                rings: [
                  RingData(progress: 0.9, color: .purple, lineWidth: 8, title: "数据1"),
                  RingData(progress: 0.7, color: .cyan, lineWidth: 7, title: "数据2"),
                  RingData(progress: 0.5, color: .pink, lineWidth: 6, title: "数据3")
                ],
                size: 100,
                spacing: 4
              )
            )
          }
          
          VStack(spacing: 10) {
            Text("单色主题")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              viewModel: HomeMultiCircularVM(
                rings: [
                  RingData(progress: 0.8, color: .blue, lineWidth: 8, title: "外环"),
                  RingData(progress: 0.6, color: .blue.opacity(0.8), lineWidth: 7, title: "中环"),
                  RingData(progress: 0.4, color: .blue.opacity(0.6), lineWidth: 6, title: "内环")
                ],
                size: 100,
                spacing: 3
              )
            )
          }
          
          VStack(spacing: 10) {
            Text("细线条")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              viewModel: HomeMultiCircularVM(
                rings: [
                  RingData(progress: 0.85, color: .orange, lineWidth: 4, title: "数据1"),
                  RingData(progress: 0.65, color: .green, lineWidth: 4, title: "数据2"),
                  RingData(progress: 0.45, color: .red, lineWidth: 4, title: "数据3")
                ],
                size: 90,
                spacing: 6
              )
            )
          }
        }
      }
      
      Divider()
      
      // MARK: 不同进度展示
      VStack(alignment: .leading, spacing: 20) {
        Text("不同进度值")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 25) {
          VStack(spacing: 10) {
            Text("低进度")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 0.2,
              middleProgress: 0.15,
              innerProgress: 0.1
            )
          }
          
          VStack(spacing: 10) {
            Text("中等进度")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 0.5,
              middleProgress: 0.4,
              innerProgress: 0.3
            )
          }
          
          VStack(spacing: 10) {
            Text("高进度")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 0.95,
              middleProgress: 0.85,
              innerProgress: 0.75
            )
          }
          
          VStack(spacing: 10) {
            Text("满进度")
              .font(.caption)
              .foregroundColor(.gray)
            
            HomeMultiCircularView(
              outerProgress: 1.0,
              middleProgress: 1.0,
              innerProgress: 1.0
            )
          }
        }
      }
      
      Divider()
      
      // MARK: 动画演示
      VStack(alignment: .leading, spacing: 20) {
        Text("动画演示")
          .font(.title2)
          .fontWeight(.bold)
        
        AnimatedCircularDemo()
      }
    }
    .padding()
  }
  .background(Color(.systemGroupedBackground))
}

// MARK: - 动画演示组件

private struct AnimatedCircularDemo: View {
  @State private var animationProgress: Double = 0
  @State private var isAnimating = false
  
  var body: some View {
    VStack(spacing: 20) {
      HomeMultiCircularView(
        outerProgress: animationProgress * 0.9,
        middleProgress: animationProgress * 0.7,
        innerProgress: animationProgress * 0.5,
        size: 120
      )
      
      HStack(spacing: 15) {
        Button(isAnimating ? "暂停动画" : "开始动画") {
          withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            if !isAnimating {
              animationProgress = 1.0
            }
          }
          isAnimating.toggle()
          
          if !isAnimating {
            withAnimation(.easeInOut(duration: 0.5)) {
              animationProgress = 0
            }
          }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(isAnimating ? Color.red : Color.blue)
        .foregroundColor(.white)
        .cornerRadius(8)
        
        Button("重置") {
          isAnimating = false
          withAnimation(.easeInOut(duration: 0.5)) {
            animationProgress = 0
          }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.gray)
        .foregroundColor(.white)
        .cornerRadius(8)
      }
    }
  }
}