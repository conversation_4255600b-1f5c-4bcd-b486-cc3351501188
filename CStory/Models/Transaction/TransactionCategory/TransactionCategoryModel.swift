//
//  TransactionCategoryModel.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import Foundation
import SwiftData

// MARK: - 主分类模型

/// 交易主分类模型
@Model
final class TransactionMainCategoryModel: Identifiable {
  /// 唯一标识符
  var id: String = UUID().uuidString
  /// 分类名称
  var name: String = ""
  /// 分类图标
  var icon: IconType = IconType.emoji("❓")
  /// 排序顺序
  var order: Int = 0
  /// 交易类型
  var type: String = TransactionType.expense.rawValue
  /// 子分类列表
  @Relationship(deleteRule: .cascade) var subCategories: [TransactionSubCategoryModel]?
  init(
    id: String, name: String, icon: IconType, order: Int, type: String,
    subCategories: [TransactionSubCategoryModel]? = nil
  ) {
    self.id = id
    self.name = name
    self.icon = icon
    self.order = order
    self.type = type
    self.subCategories = subCategories
  }
}

// MARK: - 子分类模型

/// 交易子分类模型
@Model
final class TransactionSubCategoryModel: Identifiable {
  /// 唯一标识符
  var id: String = UUID().uuidString
  /// 分类名称
  var name: String = ""
  /// 分类图标
  var icon: IconType = IconType.emoji("❓")
  /// 排序顺序
  var order: Int = 0
  /// 主分类ID
  var mainId: String = ""
  /// 关联的主分类
  @Relationship(inverse: \TransactionMainCategoryModel.subCategories) var mainCategory:
    TransactionMainCategoryModel?
  init(
    id: String, name: String, icon: IconType, order: Int, mainId: String,
    mainCategory: TransactionMainCategoryModel? = nil
  ) {
    self.id = id
    self.name = name
    self.icon = icon
    self.order = order
    self.mainId = mainId
    self.mainCategory = mainCategory
  }
}
