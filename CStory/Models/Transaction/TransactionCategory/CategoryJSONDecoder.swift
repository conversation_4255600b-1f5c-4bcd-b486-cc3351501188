//
//  CategoryJSONDecoder.swift
//  CStory
//
//  Created by NZUE on 2024/12/30.
//

import Foundation

// MARK: - 响应模型

/// 图标响应模型
struct IconResponse: Decodable {
  /// 图标类型
  let type: String
  /// 图标值
  let value: String
}

/// 主分类响应模型
struct MainCategoryResponse: Decodable {
  /// 分类ID
  let categoryId: String
  /// 分类名称
  let name: String
  /// 图标信息
  let icon: IconResponse
  /// 排序顺序
  let order: Int
  /// 类型
  let type: String
  /// 子分类列表
  let subCategories: [SubCategoryResponse]?

  /// 获取图标类型
  /// 
  /// 将本地图片转换为Data存储到数据库。
  var iconType: IconType {
    switch icon.type {
    case "emoji":
      return IconType.emoji(icon.value)
    case "image":
      // 从Assets加载图片并转换为Data，这样会存储到数据库并同步到CloudKit
      if let imageData = ImageLoaderHelper.loadCategoryImageData(for: categoryId) {
        return IconType.image(imageData)
      } else {
        // 如果图片加载失败，使用默认emoji
        print("⚠️ 主分类 \(categoryId)(\(name)) 图片加载失败，使用默认emoji")
        return IconType.emoji("❓")
      }
    default:
      // 默认使用emoji
      return IconType.emoji(icon.value)
    }
  }

  /// 获取交易类型字符串
  var transactionType: String {
    return type
  }
}

/// 子分类响应模型
struct SubCategoryResponse: Decodable {
  /// 分类ID
  let categoryId: String
  /// 分类名称
  let name: String
  /// 图标信息
  let icon: IconResponse
  /// 排序顺序
  let order: Int

  /// 获取图标类型
  /// 
  /// 将本地图片转换为Data存储到数据库。
  var iconType: IconType {
    switch icon.type {
    case "emoji":
      return IconType.emoji(icon.value)
    case "image":
      // 从Assets加载图片并转换为Data，这样会存储到数据库并同步到CloudKit
      if let imageData = ImageLoaderHelper.loadCategoryImageData(for: categoryId) {
        return IconType.image(imageData)
      } else {
        // 如果图片加载失败，使用默认emoji
        print("⚠️ 子分类 \(categoryId)(\(name)) 图片加载失败，使用默认emoji")
        return IconType.emoji("❓")
      }
    default:
      // 默认使用emoji
      return IconType.emoji(icon.value)
    }
  }
}

// MARK: - JSON 解码器

/// 主分类 JSON 解码器
struct MainCategoryJSONDecoder {
  /// 解码分类数据
  /// - Parameter fileName: JSON 文件名（不含扩展名）
  /// - Returns: 解码后的主分类数组
  static func decode(from fileName: String) -> [MainCategoryResponse] {
    guard let url = Bundle.main.url(forResource: fileName, withExtension: "json"),
          let data = try? Data(contentsOf: url)
    else {
      print("❌ 无法找到或读取JSON文件: \(fileName).json")
      return []
    }
    
    let decoder = JSONDecoder()
    
    do {
      let response = try decoder.decode(CategoriesResponse.self, from: data)
      return response.categories
    } catch {
      print("❌ 解码JSON文件时出错：\(error)")
      return []
    }
  }
}

/// 分类响应容器
struct CategoriesResponse: Decodable {
  /// 分类列表
  let categories: [MainCategoryResponse]
}
