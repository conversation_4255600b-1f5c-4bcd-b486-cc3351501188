//
//  SpeechRecognitionHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Combine
import Speech

/// 语音识别管理器
class SpeechRecognitionHelper: ObservableObject {
  /// 实时转录的文本
  @Published var transcribedText: String = ""
  /// 音频输入级别（0.0-1.0）
  @Published var audioLevel: CGFloat = 0.0

  private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
  private var recognitionTask: SFSpeechRecognitionTask?
  private let audioEngine = AVAudioEngine()
  private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

  /// 开始录音和识别
  func startRecording() {
    SFSpeechRecognizer.requestAuthorization { [weak self] authStatus in
      guard let self = self else { return }
      if authStatus == .authorized {
        Task { @MainActor in
          do {
            try await self.startRecordingAndRecognition()
          } catch {
            print("录音启动失败: \(error)")
          }
        }
      }
    }
  }

  /// 停止录音并返回识别结果
  func stopRecording() -> String {
    audioEngine.stop()
    audioEngine.inputNode.removeTap(onBus: 0)
    recognitionRequest?.endAudio()
    recognitionRequest = nil
    recognitionTask?.cancel()
    recognitionTask = nil
    audioLevel = 0

    let finalText = transcribedText
    transcribedText = ""
    return finalText
  }

  /// 取消录音（不保存结果）
  func cancelRecording() {
    audioEngine.stop()
    audioEngine.inputNode.removeTap(onBus: 0)
    recognitionRequest?.endAudio()
    recognitionRequest = nil
    recognitionTask?.cancel()
    recognitionTask = nil
    audioLevel = 0
    transcribedText = ""
  }

  /// 计算音频缓冲区的音量级别
  private func calculateLevel(_ buffer: AVAudioPCMBuffer) -> CGFloat {
    let channelData = buffer.floatChannelData?[0]
    let frameLength = UInt32(buffer.frameLength)

    var sum: Float = 0
    for i in 0..<Int(frameLength) {
      let sample = channelData?[i] ?? 0
      sum += sample * sample
    }

    let rms = sqrt(sum / Float(frameLength))
    return CGFloat(rms * 10)
  }

  /// 启动录音和识别
  private func startRecordingAndRecognition() async throws {
    // 停止之前的任务
    recognitionTask?.cancel()
    recognitionTask = nil

    // 停止音频引擎
    if audioEngine.isRunning {
      audioEngine.stop()
      audioEngine.inputNode.removeTap(onBus: 0)
    }

    let audioSession = AVAudioSession.sharedInstance()
    try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
    try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

    recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

    guard let recognitionRequest = recognitionRequest else {
      throw NSError(domain: "Speech", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法创建语音识别请求"])
    }

    recognitionRequest.shouldReportPartialResults = true

    // 设置本地识别（如果可用）
    if #available(iOS 13.0, *) {
      recognitionRequest.requiresOnDeviceRecognition = false
    }

    recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) {
      [weak self] result, _ in
      guard let self = self else { return }

      if let result = result {
        Task { @MainActor in
          self.transcribedText = result.bestTranscription.formattedString
        }
      }
    }

    let inputNode = audioEngine.inputNode
    let recordingFormat = inputNode.outputFormat(forBus: 0)

    inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) {
      [weak self] buffer, _ in
      guard let self = self else { return }
      // 处理语音识别
      self.recognitionRequest?.append(buffer)

      // 计算音量级别
      let level = self.calculateLevel(buffer)
      Task { @MainActor in
        self.audioLevel = level
      }
    }

    audioEngine.prepare()
    try audioEngine.start()
  }
}
