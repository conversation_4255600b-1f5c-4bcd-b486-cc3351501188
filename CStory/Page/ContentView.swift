//
//  ContentView.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import SwiftData
import SwiftUI

struct ContentView: View {
  @Environment(\.modelContext) private var modelContext
  @ObservedObject private var pathManager: PathManagerHelper
  @ObservedObject private var dataManager: DataManagement

  // MARK: - 集中式数据查询
  @Query private var cards: [CardModel]
  @Query private var mainCategories: [TransactionMainCategoryModel]
  @Query private var subCategories: [TransactionSubCategoryModel]
  @Query private var currencies: [CurrencyModel]
  @Query(sort: \TransactionModel.transactionDate, order: .reverse)
  private var allTransactions: [TransactionModel]
  @Query(sort: \ChatMessageModel.timestamp, order: .reverse)
  private var chatMessages: [ChatMessageModel]

  // MARK: - 计算属性
  private var recentTransactions: [TransactionModel] {
    Array(allTransactions.prefix(100))
  }

  // MARK: - 初始化
  init() {
    // 创建管理器实例
    let pathMgr = PathManagerHelper()
    let dataMgr = DataManagement()
    
    self.pathManager = pathMgr
    self.dataManager = dataMgr
  }

  var body: some View {
    // 自动更新dataManager - 利用@ObservedObject的响应式特性
    let _ = updateDataManagerAutomatically()
    
    return NavigationStack(path: $pathManager.path) {
      VStack {
        HomeView()
          .navigationDestination(for: NavigationDestination.self) { destination in
            destination.destinationView(modelContext: modelContext)
          }
      }
    }
    .withDataManager(dataManager)
    .environmentObject(pathManager)
  }
  
  // MARK: - 自动数据更新
  private func updateDataManagerAutomatically() {
    // 利用SwiftUI的响应式机制，当@Query数据变化时，这个函数会自动调用
    dataManager.updateData(
      cards: cards,
      mainCategories: mainCategories,
      subCategories: subCategories,
      currencies: currencies,
      recentTransactions: recentTransactions,
      allTransactions: allTransactions,
      chatMessages: chatMessages
    )
  }
}

#Preview {
  ContentView()
    .modelContainer(
      for: [
        CardModel.self,
        TransactionModel.self,
        TransactionMainCategoryModel.self,
        TransactionSubCategoryModel.self,
        CurrencyModel.self,
      ], inMemory: true)
}
