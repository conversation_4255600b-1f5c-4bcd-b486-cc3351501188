//
//  HomeVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

// MARK: - 卡片显示数据结构

/// 卡片显示数据结构，用于UI层展示
///
/// 该结构体包含了UI层显示卡片所需的所有格式化数据，避免UI层直接访问业务模型。
/// 所有的业务逻辑处理（如货币转换、颜色计算等）都在ViewModel层完成。
///
/// - Note: 该结构体遵循MVVM架构原则，将业务逻辑与UI展示分离
struct HomeCardDisplayData: Identifiable {
  /// 卡片唯一标识符
  let id: UUID

  /// 卡片余额（已转换为本位货币）
  let balance: Double

  /// 货币符号（本位货币符号）
  let symbol: String

  /// 是否为信用卡
  let isCredit: Bool

  /// 卡片背景图片名称（由CardCoverHelper处理）
  let coverImageName: String

  /// 文本颜色（根据背景深浅计算）
  let textColor: Color

  /// 银行Logo数据（可选）
  let bankLogo: Data?
}

/// 主页视图模型
///
/// 负责主页的业务逻辑处理和数据管理，遵循MVVM架构模式。
/// 该类从DataManagement获取原始数据，进行业务逻辑处理后，
/// 为HomeTestView提供格式化的、可直接使用的数据。
///
/// ## 主要职责
/// - 资产统计计算（净资产、总资产、总负债）
/// - 最近交易数据处理和格式化
/// - 卡片数据处理和UI适配
/// - 问候语生成
/// - 货币转换和本地化处理
///
/// ## 数据流向
/// ```
/// DataManagement → HomeVM → HomeTestView
/// ```
///
/// ## 使用示例
/// ```swift
/// let homeVM = HomeVM(dataManager: dataManager)
/// // homeVM会自动计算所有需要的数据
/// ```
///
/// - Important: 该类使用CurrencyService进行标准的资产计算，
///   确保与正式项目的计算逻辑完全一致
/// - Note: 所有计算结果都会转换为用户设置的本位货币
/// - Author: 咩咩
/// - Version: 1.0
/// - Since: 2025.7.18
final class HomeVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供原始数据源
  ///
  /// 通过DataManagement获取卡片、交易、货币等基础数据，
  /// 作为所有业务逻辑计算的数据来源。
  private let dataManager: DataManagement

  /// 交易点击回调
  private let onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - Published Properties

  // MARK: 问候语数据

  /// 根据当前时间生成的问候语消息
  ///
  /// 根据当前小时自动生成相应的问候语：
  /// - 5-12点：早上好！
  /// - 12-18点：下午好！
  /// - 18-22点：晚上好！
  /// - 其他时间：夜深了！
  @Published var greetingMessage: String

  // MARK: 资产统计数据

  /// 净资产金额（本位货币）
  ///
  /// 通过CurrencyService标准算法计算：净资产 = 总资产 - 总负债
  /// 所有金额都已转换为用户设置的本位货币。
  @Published var netAssetAmount: Double

  /// 本位货币符号
  ///
  /// 用户设置的本位货币符号，如"¥"、"$"等，
  /// 用于在UI中显示货币金额。
  @Published var netAssetSymbol: String

  // MARK: 交易数据

  /// 最近7日交易的UI显示数据
  ///
  /// 已经过滤、排序和格式化的交易数据，可直接用于UI显示。
  /// 包含分类信息、时间格式化、金额颜色等UI所需的所有数据。
  @Published var recentTransactionRows: [TransactionRowVM] = []

  /// 是否有最近交易数据
  ///
  /// 用于UI判断是否显示交易列表或空状态占位视图。
  @Published var hasRecentTransactions: Bool = false

  // MARK: 卡片数据

  /// 主页卡片的UI显示数据
  ///
  /// 包含所有选中卡片的格式化显示数据，包括背景图片、文本颜色等UI属性。
  /// 数据已按用户设置的order排序。
  @Published var homeCardDisplayData: [HomeCardDisplayData] = []

  // MARK: 统计卡片数据

  /// 总资产统计卡片的ViewModel
  ///
  /// 预先准备好的总资产统计卡片数据，包含标题、金额、货币符号和图标。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var totalAssetCardVM: HomeStatisticCardVM

  /// 总负债统计卡片的ViewModel
  ///
  /// 预先准备好的总负债统计卡片数据，包含标题、金额、货币符号和图标。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var totalLiabilityCardVM: HomeStatisticCardVM

  /// 多圆环进度的ViewModel
  ///
  /// 预先准备好的多圆环进度数据，基于资产统计计算进度值。
  /// View层可以直接使用，无需在UI层计算进度。
  @Published var homeMultiCircularVM: HomeMultiCircularVM

  // MARK: 圆环数据解释相关属性

  /// 储蓄净值占比
  ///
  /// 储蓄资产净值在总资产中的比例
  @Published var savingsRatio: Double = 0.0

  /// 信用额度可用率
  ///
  /// 信用卡剩余额度占总额度比例
  @Published var creditAvailableRatio: Double = 0.0

  /// 现金流健康度
  ///
  /// 近30天收入在总收支中的比例
  @Published var cashFlowHealthScore: Double = 0.0

  // MARK: - Initialization

  /// 初始化主页视图模型
  ///
  /// 创建HomeVM实例并自动计算所有需要的数据。
  /// 初始化完成后，所有Published属性都会包含最新的计算结果。
  ///
  /// - Parameter dataManager: 数据管理器，提供基础数据源
  ///
  /// ## 初始化流程
  /// 1. 保存数据管理器引用
  /// 2. 初始化所有Published属性为默认值
  /// 3. 调用calculateData()计算实际数据
  /// 4. 触发UI更新
  ///
  /// - Note: 初始化过程是同步的，完成后即可使用所有计算结果
  init(dataManager: DataManagement, onTransactionTap: ((TransactionModel) -> Void)? = nil) {
    self.dataManager = dataManager
    self.onTransactionTap = onTransactionTap

    // 初始化Published属性为默认值
    self.greetingMessage = ""
    self.netAssetAmount = 0.0
    self.netAssetSymbol = ""

    // 初始化统计卡片ViewModels为默认值
    self.totalAssetCardVM = HomeStatisticCardVM(
      title: "总资产",
      amount: 0.0,
      currencySymbol: "¥",
      iconName: "TotalCardsIcon"
    )
    self.totalLiabilityCardVM = HomeStatisticCardVM(
      title: "总负债",
      amount: 0.0,
      currencySymbol: "¥",
      iconName: "TotalLiabilitiesIcon"
    )

    // 初始化多圆环进度ViewModel为默认值
    self.homeMultiCircularVM = HomeMultiCircularVM.assetStatistics(
      totalAsset: 0.0,
      totalLiability: 0.0,
      netAsset: 0.0,
      size: 90
    )

    // 初始化圆环数据解释相关属性
    self.savingsRatio = 0.0
    self.creditAvailableRatio = 0.0
    self.cashFlowHealthScore = 0.0

    // 计算所有实际数据
    calculateData()
  }

  // MARK: - Private Methods

  /// 计算所有业务数据
  ///
  /// 统一入口方法，按顺序计算所有需要的数据：
  /// 1. 问候语生成
  /// 2. 资产统计计算
  /// 3. 最近交易处理
  /// 4. 卡片数据处理
  ///
  /// - Note: 该方法在初始化时自动调用，确保所有数据都是最新的
  private func calculateData() {
    calculateGreeting()
    calculateAssetStatistics()
    calculateRecentTransactions()
    calculateHomeCards()
  }

  /// 根据当前时间生成问候语
  ///
  /// 基于当前系统时间的小时数，生成相应的问候语消息。
  /// 时间段划分遵循中文习惯：
  ///
  /// - 5-12点：早上好！
  /// - 12-18点：下午好！
  /// - 18-22点：晚上好！
  /// - 其他时间：夜深了！
  ///
  /// - Note: 使用系统当前时间，会随时间变化自动更新
  private func calculateGreeting() {
    let hour = Calendar.current.component(.hour, from: Date())
    switch hour {
    case 5..<12:
      greetingMessage = "早上好！"
    case 12..<18:
      greetingMessage = "下午好！"
    case 18..<22:
      greetingMessage = "晚上好！"
    default:
      greetingMessage = "夜深了！"
    }
  }

  /// 计算资产统计数据
  ///
  /// 使用CurrencyService的标准算法计算用户的资产统计信息，
  /// 确保与正式项目的计算逻辑完全一致。
  ///
  /// ## 计算内容
  /// - **总资产**: 储蓄卡正余额 + 信用卡溢缴款
  /// - **总负债**: 储蓄卡透支 + 信用卡欠款
  /// - **净资产**: 总资产 - 总负债
  /// - **货币符号**: 用户设置的本位货币符号
  ///
  /// ## 计算规则
  /// - 只统计`isSelected`和`isStatistics`都为true的卡片
  /// - 所有金额都转换为用户设置的本位货币
  /// - 使用标准汇率进行货币转换
  ///
  /// - Important: 使用CurrencyService.calculateCardsDetailed确保计算准确性
  /// - Note: 计算结果会自动更新到对应的Published属性
  private func calculateAssetStatistics() {
    // 获取用户设置的本位货币信息
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    netAssetSymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"

    // 使用CurrencyService的标准计算方法
    let result = CurrencyService.shared.calculateCardsDetailed(
      cards: dataManager.cards,
      currencies: dataManager.currencies,
      baseCurrencyCode: baseCurrencyCode,
      filterCreditCard: nil,  // 计算所有类型的卡片
      debug: false
    )

    // 更新Published属性
    netAssetAmount = result.netCards

    // 更新统计卡片ViewModels的数据
    totalAssetCardVM.amount = result.totalCards
    totalAssetCardVM.currencySymbol = netAssetSymbol

    totalLiabilityCardVM.amount = result.totalLiabilities
    totalLiabilityCardVM.currencySymbol = netAssetSymbol

    // 计算圆环数据解释相关的指标
    calculateCircleExplanationData(result: result)

    // 更新多圆环进度ViewModel的数据 - 使用业务指标数据和对应颜色
    homeMultiCircularVM = HomeMultiCircularVM(
      rings: [
        RingData(progress: savingsRatio, color: Color.accentColor, lineWidth: 8, title: "储蓄净值占比"),
        RingData(progress: creditAvailableRatio, color: .green, lineWidth: 8, title: "信用额度可用率"),
        RingData(progress: cashFlowHealthScore, color: .orange, lineWidth: 8, title: "现金流健康度"),
      ],
      size: 90
    )
  }

  /// 计算圆环数据解释相关指标
  private func calculateCircleExplanationData(result: CurrencyService.CardCalculationResult) {
    // 1. 储蓄净值占比：储蓄卡净值在总资产中的比例
    let savingsCards = dataManager.cards.filter { !$0.isCredit }
    var savingsNetValue: Double = 0

    for card in savingsCards {
      let cardBalance = CurrencyService.shared.convertAmount(
        card.balance,
        from: card.currency,
        to: CurrencyService.shared.baseCurrencyCode,
        currencies: dataManager.currencies
      )
      if cardBalance > 0 {
        savingsNetValue += cardBalance
      }
    }

    savingsRatio = result.totalCards > 0 ? min(1.0, savingsNetValue / result.totalCards) : 0.0

    // 2. 信用额度可用率：信用卡剩余额度占总额度比例
    let creditCards = dataManager.cards.filter { $0.isCredit }
    var totalCreditLimit: Double = 0
    var usedCreditAmount: Double = 0

    for card in creditCards {
      let creditLimit = CurrencyService.shared.convertAmount(
        card.credit,
        from: card.currency,
        to: CurrencyService.shared.baseCurrencyCode,
        currencies: dataManager.currencies
      )
      let cardBalance = CurrencyService.shared.convertAmount(
        card.balance,
        from: card.currency,
        to: CurrencyService.shared.baseCurrencyCode,
        currencies: dataManager.currencies
      )

      totalCreditLimit += creditLimit
      if cardBalance < 0 {
        usedCreditAmount += abs(cardBalance)
      }
    }

    creditAvailableRatio =
      totalCreditLimit > 0
      ? min(1.0, max(0.0, (totalCreditLimit - usedCreditAmount) / totalCreditLimit)) : 0.0

    // 3. 现金流健康度：近30天收入在总收支中的比例
    let calendar = Calendar.current
    let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()

    // 使用 TransactionQueryService 获取最近30天的交易
    let allTransactions = dataManager.allTransactions.filter { transaction in
      transaction.transactionDate >= thirtyDaysAgo
    }
    let recentTransactions = TransactionQueryService.shared.filterTransactions(
      allTransactions,
      byTypes: [.income, .expense, .transfer, .refund]
    )

    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in recentTransactions {
      let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: CurrencyService.shared.baseCurrencyCode
      )
      totalIncome += amounts.income
      totalExpense += amounts.expense
    }

    let totalCashFlow = totalIncome + totalExpense
    cashFlowHealthScore = totalCashFlow > 0 ? min(1.0, totalIncome / totalCashFlow) : 0.0
  }

  /// 计算最近交易的UI显示数据
  ///
  /// 获取最近7日的交易记录，并转换为UI可直接使用的格式。
  /// 包含交易分类、时间格式化、金额颜色等所有UI所需信息。
  ///
  /// ## 处理流程
  /// 1. 获取最近7日交易（排除系统交易）
  /// 2. 查找关联的卡片信息
  /// 3. 获取交易分类信息
  /// 4. 创建TransactionRowVM实例
  /// 5. 更新交易状态标识
  ///
  /// ## 数据转换
  /// - **时间格式化**: 今天/昨天/前天/具体日期
  /// - **金额处理**: 支出负数、收入正数、颜色区分
  /// - **分类信息**: 主分类-子分类名称和图标
  ///
  /// - Note: 转换后的数据可直接用于TransactionRow组件显示
  /// - SeeAlso: `TransactionRowVM.init(transaction:relatedCard:categoryInfo:onTap:)`
  private func calculateRecentTransactions() {
    // 获取最近7日的交易数据
    let recent7DaysTransactions = getRecent7DaysTransactions()

    // 转换为UI显示格式
    recentTransactionRows = recent7DaysTransactions.map { transaction in
      let relatedCard =
        dataManager.findCard(by: transaction.fromCardId)
        ?? dataManager.findCard(by: transaction.toCardId)
      let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

      return TransactionRowVM(
        transaction: transaction,
        relatedCard: relatedCard,
        categoryInfo: categoryInfo,
        onTap: {
          self.onTransactionTap?(transaction)
        }
      )
    }

    // 更新交易状态标识
    hasRecentTransactions = !recentTransactionRows.isEmpty
  }

  /// 计算主页卡片的UI显示数据
  ///
  /// 获取用户选中的卡片，并转换为UI层可直接使用的显示格式。
  /// 包含卡片背景、文本颜色等所有UI渲染所需的属性。
  ///
  /// ## 处理流程
  /// 1. 筛选用户选中的卡片（`isSelected = true`）
  /// 2. 按用户设置的顺序排序（`order`字段）
  /// 3. 使用CardCoverHelper处理卡片背景
  /// 4. 根据背景深浅计算文本颜色
  /// 5. 生成HomeCardDisplayData实例
  ///
  /// ## UI适配处理
  /// - **背景图片**: 通过CardCoverHelper获取标准背景
  /// - **文本颜色**: 根据背景深浅自动调整（深色背景用白字，浅色背景用黑字）
  /// - **银行Logo**: 保留原始数据供UI显示
  /// - **排序**: 按用户自定义的order字段排序
  ///
  /// - Note: 处理后的数据遵循UI层的显示需求，避免UI层直接访问业务模型
  /// - SeeAlso: `HomeCardDisplayData`, `CardCoverHelper`
  private func calculateHomeCards() {
    // 筛选、排序并转换为UI显示格式
    homeCardDisplayData = dataManager.cards
      .filter { $0.isSelected }
      .sorted { $0.order < $1.order }
      .map { card in
        // 使用CardCoverHelper处理卡片背景
        let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
        let coverImageName = CardCoverHelper.shared.getCoverImageName(for: coverType)
        let isDark = CardCoverHelper.shared.isCoverDark(for: coverType)
        let textColor = isDark ? Color.white : Color.cBlack

        return HomeCardDisplayData(
          id: card.id,
          balance: card.balance,
          symbol: card.symbol,
          isCredit: card.isCredit,
          coverImageName: coverImageName,
          textColor: textColor,
          bankLogo: card.bankLogo
        )
      }
  }

  /// 获取最近7日的有效交易记录
  ///
  /// 从数据源中筛选出最近7日内的用户交易，排除系统自动生成的交易。
  /// 返回的交易按时间倒序排列，最新的交易在前面。
  ///
  /// ## 筛选条件
  /// - **时间范围**: 最近7日内（包含今天）
  /// - **交易类型**: 排除`.createCard`和`.adjustCard`系统交易
  /// - **包含类型**: `.income`、`.expense`、`.transfer`、`.refund`
  ///
  /// ## 排序规则
  /// 按交易日期倒序排列（`transactionDate`降序），确保最新交易显示在前面
  ///
  /// - Returns: 符合条件的交易数组，按时间倒序排列
  /// - Note: 如果无法计算7天前的日期，返回空数组
  /// - Warning: 该方法依赖系统日期，确保设备时间准确
  private func getRecent7DaysTransactions() -> [TransactionModel] {
    let calendar = Calendar.current
    let today = Date()

    // 使用 TransactionQueryService 获取最近7天的交易
    let allRecentTransactions = dataManager.recentTransactions
    let filteredTransactions = TransactionQueryService.shared.filterTransactions(
      allRecentTransactions,
      byTypes: [.income, .expense, .transfer, .refund]
    )

    // 按日期范围筛选最近7天
    guard let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: today) else {
      return []
    }

    return filteredTransactions.filter { transaction in
      transaction.transactionDate >= sevenDaysAgo && transaction.transactionDate <= today
    }.sorted { $0.transactionDate > $1.transactionDate }
  }

}
