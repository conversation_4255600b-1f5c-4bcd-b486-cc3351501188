//
//  CardDetailView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import SwiftUI

/// 简单的卡片详情遮罩视图
///
/// 以overlay形式展示卡片的交易记录，借鉴CardDetailOverlayView的样式
struct CardDetailView: View {

  // MARK: - 传入参数

  /// 要展示的卡片
  let card: CardModel

  /// 控制显示状态
  @Binding var isPresented: Bool

  /// 路径管理器
  let pathManager: PathManagerHelper

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 状态

  @State private var showBackground = false
  @State private var transactionDayGroups: [TransactionDayGroupWithRowVM] = []
  @State private var hasTransactions = false

  // MARK: - 初始化

  init(
    card: CardModel,
    isPresented: Binding<Bool>,
    pathManager: PathManagerHelper
  ) {
    self.card = card
    self._isPresented = isPresented
    self.pathManager = pathManager
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
      // 背景遮罩
      Color.black.opacity(0.3)
        .opacity(showBackground ? 1 : 0)
        .ignoresSafeArea()
        .onTapGesture {
          dismissView()
        }

      // 主内容
      VStack(spacing: 24) {
        // 卡片展示
        Card(viewModel: CardVM.fromCard(card))
          .padding(.horizontal, 16)

        // 交易记录内容
        transactionContentView
      }
      .padding(.top, 16)
      .background(.regularMaterial)
      .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    .onAppear {
      setupAnimations()
      loadTransactionData()
    }
  }

  // MARK: - 子视图

  /// 交易记录内容视图
  private var transactionContentView: some View {
    ScrollView {
      LazyVStack(spacing: 0) {
        // 标题区域
        HStack {
          Text("交易记录")
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()

          // 关闭按钮
          Button(action: {
            dismissView()
          }) {
            Image(systemName: "xmark")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.6))
              .frame(width: 32, height: 32)
              .background(Color.cBlack.opacity(0.08))
              .clipShape(Circle())
          }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 12)

        // 交易列表内容
        TransactionListContent(
          transactionDayGroups: transactionDayGroups,
          currencySymbol: card.symbol,
          hasTransactions: hasTransactions,
          emptyStateConfig: TransactionListContentVM.EmptyStateConfig(
            icon: "404",
            text: "暂无交易记录",
            useSystemIcon: false
          )
        )

        // 底部间距
        Spacer(minLength: 80)
      }
      .padding(.top, 16)
    }
  }

  // MARK: - 数据加载

  /// 加载交易数据
  private func loadTransactionData() {
    // 获取与当前卡片相关的交易
    let cardTransactions = dataManager.allTransactions.filter { transaction in
      transaction.fromCardId == card.id || transaction.toCardId == card.id
    }

    // 按日期分组
    let groupedByDate = Dictionary(grouping: cardTransactions) { transaction in
      Calendar.current.startOfDay(for: transaction.transactionDate)
    }

    // 生成UI数据
    let dayGroups = groupedByDate.map { date, transactions in
      // 计算当日收支
      var dayIncome: Double = 0
      var dayExpense: Double = 0

      for transaction in transactions {
        let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: "CNY"
        )
        dayIncome += amounts.income
        dayExpense += amounts.expense
      }

      // 生成TransactionRowVM数组
      let transactionRowVMs =
        transactions
        .sorted { $0.transactionDate > $1.transactionDate }
        .map { transaction in
          let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

          return TransactionRowVM(
            transaction: transaction,
            relatedCard: card,
            categoryInfo: categoryInfo,
            displayContext: .cardDetail,
            onTap: {
              pathManager.path.append(
                NavigationDestination.transactionDetailView(transaction.id))
            }
          )
        }

      return (
        date: date,
        group: TransactionDayGroupWithRowVM(
          dateText: DateFormattingHelper.shared.formatDateHeader(date: date),
          dayIncome: dayIncome,
          dayExpense: dayExpense,
          transactionRowVMs: transactionRowVMs
        )
      )
    }.sorted { first, second in
      return first.date > second.date
    }.map { $0.group }

    // 更新状态
    self.transactionDayGroups = dayGroups
    self.hasTransactions = !dayGroups.isEmpty
  }

  // MARK: - 私有方法

  /// 设置动画
  private func setupAnimations() {
    withAnimation(.easeInOut(duration: 0.2)) {
      showBackground = true
    }
  }

  /// 关闭视图
  private func dismissView() {
    withAnimation(.easeInOut(duration: 0.3)) {
      showBackground = false
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      isPresented = false
    }
  }
}

// MARK: - 预览

#Preview {
  @State var isPresented = true

  let sampleCard = CardModel(
    id: UUID(),
    order: 1,
    isCredit: false,
    isSelected: true,
    name: "招商银行储蓄卡",
    remark: "",
    currency: "CNY",
    symbol: "¥",
    balance: 5280.50,
    credit: 0,
    isStatistics: true,
    cover: "Card_CS_1",
    bankName: "招商银行",
    cardNumber: "1234",
    isFixedDueDay: true,
    createdAt: Date(),
    updatedAt: Date()
  )

  return CardDetailView(
    card: sampleCard,
    isPresented: $isPresented,
    pathManager: PathManagerHelper()
  )
  .environment(\.dataManager, DataManagement.preview)
}
