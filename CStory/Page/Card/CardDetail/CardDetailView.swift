//
//  CardDetailView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import SwiftUI

/// 卡片详情遮罩视图
///
/// 以overlay形式展示卡片的交易记录，支持：
/// - 卡片基本信息展示
/// - 交易记录列表
/// - 时间筛选控制
/// - 收入支出统计
/// - 跳转到完整的卡片详情页面
struct CardDetailView: View {

  // MARK: - 传入参数

  /// 要展示的卡片
  let card: CardModel

  /// 控制显示状态
  @Binding var isPresented: Bool

  /// 路径管理器
  let pathManager: PathManagerHelper

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - ViewModel

  @StateObject private var viewModel: CardDetailVM

  // MARK: - 动画状态

  @State private var showBackground = false
  @State private var showContent = false

  // MARK: - 初始化

  init(
    card: CardModel,
    isPresented: Binding<Bool>,
    pathManager: PathManagerHelper
  ) {
    self.card = card
    self._isPresented = isPresented
    self.pathManager = pathManager

    // 创建ViewModel，但数据管理器会在onAppear时设置
    self._viewModel = StateObject(
      wrappedValue: CardDetailVM(
        card: card,
        dataManager: DataManagement.preview,  // 临时值，会在onAppear时更新
        pathManager: pathManager
      ))
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
      // 背景遮罩
      Color.black.opacity(0.3)
        .opacity(showBackground ? 1 : 0)
        .ignoresSafeArea()
        .onTapGesture {
          dismissView()
        }

      // 主内容
      VStack(spacing: 0) {
        // 顶部卡片展示
        cardHeaderView

        // 内容区域
        contentView
      }
      .background(.regularMaterial)
      .cornerRadius(24, corners: [.topLeft, .topRight])
      .offset(y: showContent ? 0 : UIScreen.main.bounds.height)
      .animation(.easeInOut(duration: 0.3), value: showContent)
    }
    .onAppear {
      // 更新ViewModel的数据管理器
      viewModel.updateDataManager(dataManager)

      // 启动动画
      setupAnimations()
    }
  }

  // MARK: - 子视图

  /// 顶部卡片展示区域
  private var cardHeaderView: some View {
    VStack(spacing: 16) {
      // 拖拽指示器
      RoundedRectangle(cornerRadius: 2)
        .fill(Color.cBlack.opacity(0.3))
        .frame(width: 36, height: 4)
        .padding(.top, 12)

      // 卡片展示
      Card(viewModel: CardVM.fromCard(card))
        .padding(.horizontal, 16)

      // 标题和关闭按钮
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(card.name)
            .font(.system(size: 18, weight: .semibold))
            .foregroundColor(.cBlack)

          Text("交易记录")
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        // 详情按钮
        Button(action: {
          // TODO: 跳转到完整的卡片详情页面
          print("跳转到卡片详情")
        }) {
          HStack(spacing: 4) {
            Text("详情")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cAccentBlue)

            Image(systemName: "chevron.right")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cAccentBlue)
          }
        }

        // 关闭按钮
        Button(action: {
          dismissView()
        }) {
          Image(systemName: "xmark")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.6))
            .frame(width: 32, height: 32)
            .background(Color.cBlack.opacity(0.08))
            .clipShape(Circle())
        }
      }
      .padding(.horizontal, 16)
    }
    .padding(.bottom, 16)
  }

  /// 内容区域
  private var contentView: some View {
    ScrollView {
      LazyVStack(spacing: 0) {
        // 时间选择区域
        timeSelectionSection

        // 收入支出统计卡片
        IncomeExpenseCard(viewModel: viewModel.incomeExpenseCardVM)
          .padding(.horizontal, 16)
          .padding(.top, 12)

        // 交易记录区域
        transactionSection

        // 底部间距
        Spacer(minLength: 40)
      }
      .padding(.top, 16)
    }
  }

  /// 时间选择区域
  private var timeSelectionSection: some View {
    HStack {
      Text("统计")
        .font(.system(size: 15, weight: .medium))
        .foregroundColor(.cBlack)

      Spacer()

      TimeControl(viewModel: viewModel.timeControlVM, style: .inline)
    }
    .padding(.horizontal, 16)
  }

  /// 交易记录区域
  private var transactionSection: some View {
    VStack(spacing: 0) {
      // 标题
      HStack {
        Text("交易记录")
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
      .padding(.bottom, 12)

      // 交易列表或空状态
      if viewModel.isLoading {
        // 加载状态
        VStack(spacing: 12) {
          ProgressView()
            .scaleEffect(0.8)

          Text("加载中...")
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
        .frame(height: 120)
      } else if let errorMessage = viewModel.errorMessage {
        // 错误状态
        VStack(spacing: 12) {
          Image(systemName: "exclamationmark.triangle")
            .font(.system(size: 32, weight: .light))
            .foregroundColor(.orange)

          Text(errorMessage)
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
            .multilineTextAlignment(.center)
        }
        .frame(height: 120)
        .padding(.horizontal, 16)
      } else {
        // 交易列表内容
        TransactionListContent(
          transactionDayGroups: viewModel.transactionDayGroups,
          currencySymbol: card.symbol,
          hasTransactions: viewModel.hasTransactions,
          emptyStateConfig: TransactionListContentVM.EmptyStateConfig(
            icon: "404",
            text: "暂无交易记录",
            useSystemIcon: false
          )
        )
      }
    }
  }

  // MARK: - 私有方法

  /// 设置动画
  private func setupAnimations() {
    withAnimation(.easeInOut(duration: 0.2)) {
      showBackground = true
    }

    withAnimation(.easeInOut(duration: 0.3).delay(0.1)) {
      showContent = true
    }
  }

  /// 关闭视图
  private func dismissView() {
    withAnimation(.easeInOut(duration: 0.3)) {
      showContent = false
      showBackground = false
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      isPresented = false
    }
  }
}

// MARK: - 圆角扩展

extension View {
  func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
    clipShape(RoundedCorner(radius: radius, corners: corners))
  }
}

struct RoundedCorner: Shape {
  var radius: CGFloat = .infinity
  var corners: UIRectCorner = .allCorners

  func path(in rect: CGRect) -> Path {
    let path = UIBezierPath(
      roundedRect: rect,
      byRoundingCorners: corners,
      cornerRadii: CGSize(width: radius, height: radius)
    )
    return Path(path.cgPath)
  }
}

// MARK: - 预览

#Preview {
  @State var isPresented = true

  let sampleCard = CardModel(
    id: UUID(),
    order: 1,
    isCredit: false,
    isSelected: true,
    name: "招商银行储蓄卡",
    remark: "",
    currency: "CNY",
    symbol: "¥",
    balance: 5280.50,
    credit: 0,
    isStatistics: true,
    cover: "Card_CS_1",
    bankName: "招商银行",
    cardNumber: "1234",
    isFixedDueDay: true,
    createdAt: Date(),
    updatedAt: Date()
  )

  return CardDetailView(
    card: sampleCard,
    isPresented: $isPresented,
    pathManager: PathManagerHelper()
  )
  .environment(\.dataManager, DataManagement.preview)
}
