//
//  CardDetailTestView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import SwiftUI

/// 卡片详情测试页面
///
/// 用于测试CardDetailView的功能，展示如何使用overlay形式的卡片详情视图
struct CardDetailTestView: View {
  
  // MARK: - 状态
  
  @State private var showingCardDetail = false
  @State private var selectedCard: CardModel?
  
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager
  
  /// 路径管理器
  @EnvironmentObject private var pathManager: PathManagerHelper
  
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared
  
  // MARK: - 主体视图
  
  var body: some View {
    NavigationView {
      ScrollView {
        VStack(spacing: 16) {
          // 标题
          Text("卡片详情测试")
            .font(.largeTitle)
            .fontWeight(.bold)
            .padding()
          
          Text("点击卡片查看交易记录")
            .font(.body)
            .foregroundColor(.secondary)
            .padding(.bottom, 20)
          
          // 卡片列表
          LazyVStack(spacing: 16) {
            ForEach(dataManager.cards) { card in
              Card(viewModel: CardVM.fromCard(card, onTap: {
                hapticManager.trigger(.selection)
                selectedCard = card
                showingCardDetail = true
              }))
            }
          }
          
          // 底部间距
          Spacer(minLength: 100)
        }
        .padding(.horizontal, 16)
      }
      .background(Color.cLightBlue)
      .navigationTitle("卡片详情测试")
      .navigationBarTitleDisplayMode(.inline)
    }
    .overlay {
      // CardDetailView overlay
      if showingCardDetail, let card = selectedCard {
        CardDetailView(
          card: card,
          isPresented: $showingCardDetail,
          pathManager: pathManager
        )
        .environment(\.dataManager, dataManager)
      }
    }
  }
}

// MARK: - 预览

#Preview {
  CardDetailTestView()
    .environment(\.dataManager, DataManagement.preview)
    .environmentObject(PathManagerHelper())
}
