//
//  CardDetailVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import Combine
import Foundation
import SwiftUI

/// 卡片详情视图模型
///
/// 负责管理卡片详情页面的数据和业务逻辑，包括：
/// - 卡片基本信息展示
/// - 交易记录列表
/// - 时间筛选控制
/// - 收入支出统计
@MainActor
final class CardDetailVM: ObservableObject {

  // MARK: - 数据源

  /// 当前卡片
  @Published var card: CardModel?

  /// 数据管理器
  private let dataManager: DataManagement

  /// 路径管理器
  private let pathManager: PathManagerHelper

  // MARK: - UI数据

  /// 收入支出卡片的ViewModel
  @Published var incomeExpenseCardVM: IncomeExpenseCardVM

  /// 时间控制器
  @Published var timeControlVM: TimeControlVM

  /// 按日期分组的交易数据
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM] = []

  /// 是否有交易数据
  @Published var hasTransactions: Bool = false

  /// 是否正在加载
  @Published var isLoading: Bool = false

  /// 错误信息
  @Published var errorMessage: String?

  // MARK: - 初始化

  init(
    card: CardModel?,
    dataManager: DataManagement,
    pathManager: PathManagerHelper
  ) {
    self.card = card
    self.dataManager = dataManager
    self.pathManager = pathManager

    // 初始化收入支出卡片ViewModel
    self.incomeExpenseCardVM = IncomeExpenseCardVM(
      income: 0,
      expense: 0,
      currencySymbol: card?.symbol ?? "¥"
    )

    // 初始化时间控制器（默认显示当月）
    self.timeControlVM = TimeControlVM(
      selectedPeriod: .month,
      currentDate: Date()
    )

    // 监听时间变化
    setupTimeControlObserver()

    // 初始加载数据
    if card != nil {
      loadTransactionData()
    }
  }

  // MARK: - 公共方法

  /// 更新数据管理器
  func updateDataManager(_ newDataManager: DataManagement) {
    self.dataManager = newDataManager
    loadTransactionData()
  }

  /// 更新卡片数据
  func updateCard(_ newCard: CardModel) {
    self.card = newCard
    self.incomeExpenseCardVM.currencySymbol = newCard.symbol
    loadTransactionData()
  }

  /// 刷新数据
  func refreshData() {
    guard card != nil else { return }
    loadTransactionData()
  }

  // MARK: - 私有方法

  /// 设置时间控制器监听
  private func setupTimeControlObserver() {
    // 监听时间控制器的变化
    timeControlVM.$selectedPeriod
      .combineLatest(timeControlVM.$currentDate)
      .dropFirst()  // 跳过初始值
      .sink { [weak self] _, _ in
        self?.loadTransactionData()
      }
      .store(in: &cancellables)
  }

  /// 加载交易数据
  private func loadTransactionData() {
    guard let card = card else { return }

    isLoading = true
    errorMessage = nil

    Task {
      do {
        let result = try await processTransactionData(for: card)

        await MainActor.run {
          self.transactionDayGroups = result.dayGroups
          self.hasTransactions = !result.dayGroups.isEmpty
          self.incomeExpenseCardVM.income = result.totalIncome
          self.incomeExpenseCardVM.expense = result.totalExpense
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.errorMessage = "加载交易数据失败: \(error.localizedDescription)"
          self.isLoading = false
        }
      }
    }
  }

  /// 处理交易数据（异步）
  private func processTransactionData(for card: CardModel) async throws -> (
    dayGroups: [TransactionDayGroupWithRowVM],
    totalIncome: Double,
    totalExpense: Double
  ) {
    // 获取时间范围
    let dateRange = timeControlVM.dateRange

    // 获取本位币代码
    let baseCurrencyCode = dataManager.currencies.first(where: { $0.isBaseCurrency })?.code ?? "CNY"

    // 筛选与当前卡片相关的交易
    let cardTransactions = dataManager.allTransactions.filter { transaction in
      // 检查交易是否与当前卡片相关
      let isRelated = transaction.fromCardId == card.id || transaction.toCardId == card.id

      // 检查交易是否在时间范围内
      let isInDateRange =
        transaction.transactionDate >= dateRange.start
        && transaction.transactionDate <= dateRange.end

      return isRelated && isInDateRange
    }

    // 计算总收入和支出
    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in cardTransactions {
      let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )
      totalIncome += amounts.income
      totalExpense += amounts.expense
    }

    // 按日期分组
    let groupedByDate = Dictionary(grouping: cardTransactions) { transaction in
      Calendar.current.startOfDay(for: transaction.transactionDate)
    }

    // 生成UI数据
    let dayGroups = groupedByDate.map { date, transactions in
      // 计算当日收支
      var dayIncome: Double = 0
      var dayExpense: Double = 0

      for transaction in transactions {
        let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: baseCurrencyCode
        )
        dayIncome += amounts.income
        dayExpense += amounts.expense
      }

      // 生成TransactionRowVM数组
      let transactionRowVMs =
        transactions
        .sorted { $0.transactionDate > $1.transactionDate }
        .map { transaction in
          let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

          return TransactionRowVM(
            transaction: transaction,
            relatedCard: card,
            categoryInfo: categoryInfo,
            displayContext: .cardDetail,
            onTap: {
              self.pathManager.path.append(
                NavigationDestination.transactionDetailView(transaction.id))
            }
          )
        }

      return (
        date: date,
        group: TransactionDayGroupWithRowVM(
          dateText: DateFormattingHelper.shared.formatDateHeader(date: date),
          dayIncome: dayIncome,
          dayExpense: dayExpense,
          transactionRowVMs: transactionRowVMs
        )
      )
    }.sorted { first, second in
      return first.date > second.date
    }.map { $0.group }

    return (dayGroups: dayGroups, totalIncome: totalIncome, totalExpense: totalExpense)
  }

  // MARK: - Combine

  private var cancellables = Set<AnyCancellable>()
}
