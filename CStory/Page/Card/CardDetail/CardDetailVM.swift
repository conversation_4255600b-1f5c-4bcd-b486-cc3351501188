//
//  CardDetailVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import Foundation

/// 简化的卡片详情视图模型
///
/// 由于CardDetailView已经简化为直接处理数据，这个VM暂时保留为空实现
/// 如果后续需要更复杂的逻辑，可以在这里扩展
class CardDetailVM: ObservableObject {

  // 暂时保留空实现，供未来扩展使用

}

  // MARK: - 公共方法

  /// 更新数据管理器
  func updateDataManager(_ newDataManager: DataManagement) {
    self.dataManager = newDataManager
    loadTransactionData()
  }

  /// 更新卡片数据
  func updateCard(_ newCard: CardModel) {
    self.card = newCard
    self.incomeExpenseCardVM.currencySymbol = newCard.symbol
    loadTransactionData()
  }

  /// 刷新数据
  func refreshData() {
    guard card != nil else { return }
    loadTransactionData()
  }

  // MARK: - 私有方法

  /// 设置时间控制器监听
  private func setupTimeControlObserver() {
    // 监听时间控制器的变化
    timeControlVM.$selectedPeriod
      .combineLatest(timeControlVM.$currentDate)
      .dropFirst()  // 跳过初始值
      .sink { [weak self] _, _ in
        self?.loadTransactionData()
      }
      .store(in: &cancellables)
  }

  /// 加载交易数据
  private func loadTransactionData() {
    guard let card = card else { return }

    isLoading = true
    errorMessage = nil

    Task {
      do {
        let result = try await processTransactionData(for: card)

        await MainActor.run {
          self.transactionDayGroups = result.dayGroups
          self.hasTransactions = !result.dayGroups.isEmpty
          self.incomeExpenseCardVM.income = result.totalIncome
          self.incomeExpenseCardVM.expense = result.totalExpense
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.errorMessage = "加载交易数据失败: \(error.localizedDescription)"
          self.isLoading = false
        }
      }
    }
  }

  /// 处理交易数据（异步）
  private func processTransactionData(for card: CardModel) async throws -> (
    dayGroups: [TransactionDayGroupWithRowVM],
    totalIncome: Double,
    totalExpense: Double
  ) {
    // 获取时间范围
    let dateRange = timeControlVM.dateRange

    // 获取本位币代码
    let baseCurrencyCode = dataManager.currencies.first(where: { $0.isBaseCurrency })?.code ?? "CNY"

    // 筛选与当前卡片相关的交易
    let cardTransactions = dataManager.allTransactions.filter { transaction in
      // 检查交易是否与当前卡片相关
      let isRelated = transaction.fromCardId == card.id || transaction.toCardId == card.id

      // 检查交易是否在时间范围内
      let isInDateRange =
        transaction.transactionDate >= dateRange.start
        && transaction.transactionDate <= dateRange.end

      return isRelated && isInDateRange
    }

    // 计算总收入和支出
    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in cardTransactions {
      let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )
      totalIncome += amounts.income
      totalExpense += amounts.expense
    }

    // 按日期分组
    let groupedByDate = Dictionary(grouping: cardTransactions) { transaction in
      Calendar.current.startOfDay(for: transaction.transactionDate)
    }

    // 生成UI数据
    let dayGroups = groupedByDate.map { date, transactions in
      // 计算当日收支
      var dayIncome: Double = 0
      var dayExpense: Double = 0

      for transaction in transactions {
        let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: baseCurrencyCode
        )
        dayIncome += amounts.income
        dayExpense += amounts.expense
      }

      // 生成TransactionRowVM数组
      let transactionRowVMs =
        transactions
        .sorted { $0.transactionDate > $1.transactionDate }
        .map { transaction in
          let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

          return TransactionRowVM(
            transaction: transaction,
            relatedCard: card,
            categoryInfo: categoryInfo,
            displayContext: .cardDetail,
            onTap: {
              self.pathManager.path.append(
                NavigationDestination.transactionDetailView(transaction.id))
            }
          )
        }

      return (
        date: date,
        group: TransactionDayGroupWithRowVM(
          dateText: DateFormattingHelper.shared.formatDateHeader(date: date),
          dayIncome: dayIncome,
          dayExpense: dayExpense,
          transactionRowVMs: transactionRowVMs
        )
      )
    }.sorted { first, second in
      return first.date > second.date
    }.map { $0.group }

    return (dayGroups: dayGroups, totalIncome: totalIncome, totalExpense: totalExpense)
  }

  // MARK: - Combine

  private var cancellables = Set<AnyCancellable>()
}
