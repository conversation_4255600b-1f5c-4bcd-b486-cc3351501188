//
//  SelectTimeSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 时间选择弹窗
///
/// 提供日期和时间选择功能的弹窗组件。
/// 使用滚轮式选择器，支持选择日期和时间（小时、分钟）。
///
/// ## 使用示例
/// ```swift
/// SelectTimeSheet(
///     title: "选择交易时间",
///     selectedDate: $transactionDate,
///     onConfirm: {
///         // 处理确认逻辑
///     },
///     onCancel: {
///         dismiss()
///     }
/// )
/// ```
struct SelectTimeSheet: View {
  // MARK: - 属性
  
  /// 弹窗标题
  let title: String
  /// 选中的日期绑定
  @Binding var selectedDate: Date
  /// 确认回调
  let onConfirm: () -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 状态变量
  
  /// 临时日期（避免直接修改绑定值）
  @State private var tempDate: Date = Date()

  // MARK: - 初始化
  init(
    title: String = "选择日期和时间",
    selectedDate: Binding<Date>,
    onConfirm: @escaping () -> Void,
    onCancel: @escaping () -> Void
  ) {
    self.title = title
    self._selectedDate = selectedDate
    self.onConfirm = onConfirm
    self.onCancel = onCancel
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: title, 
        button: "xmark.circle.fill",
        rightButtonAction: {
          onCancel()
        }
      )

      // 日期选择器
      DatePicker(
        title,
        selection: $tempDate,
        displayedComponents: [.date, .hourAndMinute]
      )
      .datePickerStyle(.wheel)
      .labelsHidden()
      .padding(.horizontal, 16)

      // 确认按钮
      Button(action: {
        selectedDate = tempDate
        onConfirm()
      }) {
        Text("确认")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .frame(height: 44)
          .background(Color.cAccentBlue)
          .cornerRadius(12)
      }
      .padding(.horizontal, 16)
      .padding(.bottom, 16)
    }
    .onAppear {
      // 初始化临时日期
      tempDate = selectedDate
    }
  }
}

#Preview {
  SelectTimeSheet(
    title: "选择日期和时间",
    selectedDate: .constant(Date()),
    onConfirm: {},
    onCancel: {}
  )
}
