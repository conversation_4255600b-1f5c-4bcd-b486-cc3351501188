//
//  CurrencyRateView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 货币汇率管理视图
///
/// 提供货币汇率的查看、更新和管理功能。支持：
/// - 查看所有货币的当前汇率
/// - 手动更新汇率数据
/// - 设置本位币
/// - 批量操作货币项
/// - 从本地JSON和API获取汇率数据
struct CurrencyRateView: View {

  // MARK: - 环境属性
  @Environment(\.dismiss) private var dismiss

  /// 数据上下文 (仅用于写操作)
  @Environment(\.modelContext) private var modelContext

  /// 导航路径管理器
  @Environment(\.presentationMode) var presentationMode

  /// 路径管理器
  @EnvironmentObject private var pathManager: PathManagerHelper

  /// 集中式数据管理器
  @Environment(\.dataManager) private var dataManager

  /// ViewModel
  @ObservedObject private var viewModel = CurrencyRateVM()

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Computed Properties

  /// 从UserDefaults获取本位币代码
  private var baseCurrencyCode: String {
    viewModel.baseCurrencyCode
  }

  /// 检查是否存在重复的货币
  private var hasDuplicates: Bool {
    viewModel.hasDuplicates(from: dataManager)
  }

  var body: some View {
    VStack(spacing: 0) {
      // 标题栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "货币汇率",
          backAction: {
            hapticManager.trigger(.impactLight)
            dismiss()
          },
          rightButton: .icon(
            "arrow.clockwise",
            action: {
              if !viewModel.isLoadingRates {
                viewModel.fetchLatestExchangeRates(using: modelContext, dataManager: dataManager)
              }
            })
        ))

      VStack(spacing: 16) {
        Button(action: {
          viewModel.showBaseCurrencySelection()
        }) {
          HStack(alignment: .center, spacing: 16) {
            // MARK: 左侧内容 (本位币图标)
            Image(systemName: "star.fill")
              .font(.system(size: 18, weight: .medium))
              .foregroundColor(.cAccentBlue)
              .frame(width: 40, height: 40)
              .background(Color.cAccentBlue.opacity(0.05))
              .cornerRadius(12)
              .overlay(
                RoundedRectangle(cornerRadius: 12)
                  .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
              )

            // MARK: 中间内容 (本位币信息)
            VStack(alignment: .leading, spacing: 4) {
              Text("本位币")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)

              if let base = dataManager.currencies.first(where: { $0.isBaseCurrency }) {
                Text("\(base.code) · \(base.symbol)")
                  .font(.system(size: 12, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.6))
              } else {
                Text("请选择本位币")
                  .font(.system(size: 12, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.6))
              }
            }

            Spacer()

            // MARK: 右侧内容 (本位币名称和箭头)
            HStack(spacing: 12) {
              if let base = dataManager.currencies.first(where: { $0.isBaseCurrency }) {
                Text(base.name)
                  .font(.system(size: 14, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.6))
              }

              Image(systemName: "chevron.forward")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }
          .padding(12)
          .background(Color.cWhite)
          .cornerRadius(16)
          .overlay(
            RoundedRectangle(cornerRadius: 16)
              .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
          )
        }
        .padding(.horizontal, 16)

        // 货币列表标题和操作按钮
        HStack {
          Text("货币列表")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()

          HStack(spacing: 12) {
            // 全选/取消全选按钮
            ActionButton(
              title: viewModel.selectAllButtonTitle(from: dataManager),
              action: {
                viewModel.toggleSelectAll(using: modelContext, dataManager: dataManager)
              }
            )

            // 去重按钮（条件性显示）
            if hasDuplicates {
              ActionButton(
                title: "去重",
                action: {
                  viewModel.removeDuplicates(using: modelContext, dataManager: dataManager)
                }
              )
            }
          }
        }
        .padding(.horizontal, 16)
        .padding(.top, 24)
        .padding(.bottom, 12)

        // 货币列表
        ScrollView {
          LazyVStack(spacing: 8) {
            ForEach(dataManager.currencies.filter { !$0.isBaseCurrency }) { currency in
              CurrencyRateRow(
                viewModel: CurrencyRateRowVM(
                  from: currency,
                  onTap: {
                    viewModel.showCurrencyOperations(currency: currency)
                  },
                  onSelectionChanged: { newValue in
                    currency.isSelected = newValue
                    try? modelContext.save()
                    // 更新全选状态
                    viewModel.updateSelectAllState(dataManager: dataManager)
                  }
                )
              )
              .contextMenu {
                Button {
                  do {
                    try currency.setAsBaseCurrency(in: modelContext)
                  } catch {
                    print("设置本位币失败: \(error)")
                  }
                } label: {
                  Label("设为本位币", systemImage: "star")
                }

                Button(role: .destructive) {
                  // 如果要删除的是本位币，设置 UserDefaults 中的为默认值
                  if currency.isBaseCurrency {
                    UserDefaults.standard.set("CNY", forKey: "baseCurrencyCode")
                  }
                  modelContext.delete(currency)
                  try? modelContext.save()
                } label: {
                  Label("删除", systemImage: "trash")
                }
              }
            }
          }
          .padding(.horizontal, 16)
        }
      }
    }
    .background(Color.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      viewModel.checkBaseCurrency(using: modelContext, dataManager: dataManager)
    }
    .onChange(of: dataManager.currencies) {
      // 当货币数据发生变化时，更新全选状态
      viewModel.updateSelectAllState(dataManager: dataManager)
    }

    .floatingSheet(
      isPresented: $viewModel.showCurrencySheet,
      config: SheetBase(
        maxDetent: .fraction(0.3),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      if let currency = viewModel.selectedCurrency {
        CurrencyRateSheet(currency: currency)
      }
    }
    .floatingSheet(
      isPresented: $viewModel.showBaseCurrencySheet,
      config: SheetBase(
        maxDetent: .fraction(0.7),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrencySheet(
        selectedCurrencyCode: .constant(
          dataManager.currencies.first(where: { $0.isBaseCurrency })?.code ?? "CNY"),
        currencySymbol: .constant(""),
        mode: "baseCurrencySelection",
        onBaseCurrencySelect: { newBaseCurrency in
          do {
            try newBaseCurrency.setAsBaseCurrency(in: modelContext)
          } catch {
            print("设置本位币失败: \(error)")
          }
        }
      )
    }

  }
}

// MARK: - 货币汇率Sheet
struct CurrencyRateSheet: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext

  // 货币数据
  var currency: CurrencyModel

  // 自定义汇率输入
  @State private var customRateText: String = ""
  // 是否显示键盘
  @State private var showKeyboard: Bool = false
  // 显示恢复默认汇率确认弹窗
  @State private var showRestoreAlert: Bool = false
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    VStack(spacing: 16) {
      // 货币信息
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(currency.name)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          Text("\(currency.code) · \(currency.symbol)")
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        // 关闭按钮
        Button(action: {
          hapticManager.trigger(.impactLight)
          dismiss()
        }) {
          Image(systemName: "xmark")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.6))
            .frame(width: 32, height: 32)
            .background(Color.cWhite)
            .cornerRadius(16)
            .overlay(
              RoundedRectangle(cornerRadius: 16)
                .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
        }
      }
      .padding(.horizontal, 16)

      // 汇率设置
      VStack(spacing: 12) {
        // 当前汇率显示
        HStack {
          Text("当前汇率")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()

          Text(formatRateWithSixDecimals(currency.rate))
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        // 自定义汇率输入
        HStack {
          Text("自定义汇率")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()

          TextField("", text: $customRateText)
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cAccentBlue)
            .multilineTextAlignment(.trailing)
            .keyboardType(.decimalPad)
            .frame(width: 100)
            .onTapGesture {
              // 初始化自定义汇率文本
              if customRateText.isEmpty {
                customRateText = formatRateWithSixDecimals(currency.rate)
              }
              showKeyboard = true
            }
        }

        // 操作按钮
        HStack(spacing: 12) {
          // 恢复默认按钮
          Button(action: {
            hapticManager.trigger(.warning)
            showRestoreAlert = true
          }) {
            Text("恢复默认")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.6))
              .padding(.horizontal, 16)
              .padding(.vertical, 8)
              .background(Color.cWhite)
              .cornerRadius(16)
              .overlay(
                RoundedRectangle(cornerRadius: 16)
                  .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
              )
          }
          .disabled(currency.isBaseCurrency || !currency.isCustom)
          .opacity(currency.isBaseCurrency || !currency.isCustom ? 0.5 : 1)

          // 确认按钮
          Button(action: {
            hapticManager.trigger(.impactMedium)
            saveCustomRate()
          }) {
            Text("确认")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cWhite)
              .padding(.horizontal, 16)
              .padding(.vertical, 8)
              .background(Color.cAccentBlue)
              .cornerRadius(16)
          }
          .disabled(currency.isBaseCurrency || customRateText.isEmpty)
          .opacity(currency.isBaseCurrency || customRateText.isEmpty ? 0.5 : 1)
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
      .padding(.horizontal, 16)

      Spacer()
    }
    .padding(.top, 16)
    .onAppear {
      // 初始化显示的自定义汇率
      if let customRate = currency.customRate {
        customRateText = formatRateWithSixDecimals(customRate)
      }
    }
    .alert("恢复默认汇率", isPresented: $showRestoreAlert) {
      Button("取消", role: .cancel) {}
      Button("确认恢复", role: .destructive) {
        restoreDefaultRate()
      }
    } message: {
      Text("确定要恢复默认汇率吗？这将清除您的自定义设置。")
    }
  }

  // 保存自定义汇率
  private func saveCustomRate() {
    guard !currency.isBaseCurrency, let newRate = Double(customRateText) else { return }

    // 确保汇率有效
    guard newRate > 0 else {
      customRateText = formatRateWithSixDecimals(currency.rate)
      return
    }

    // 更新汇率
    currency.customRate = newRate
    currency.rate = newRate
    currency.isCustom = true
    currency.updatedAt = Date()

    // 保存更改
    try? modelContext.save()

    // 关闭键盘和sheet
    showKeyboard = false
    dismiss()
  }

  // 恢复默认汇率
  private func restoreDefaultRate() {
    currency.restoreDefaultRate()
    try? modelContext.save()
    dismiss()
  }

  // 格式化汇率为6位小数，并去除尾随0
  private func formatRateWithSixDecimals(_ rate: Double) -> String {
    return NumberFormatService.shared.formatExchangeRate(rate, maxDecimals: 6)
  }
}
