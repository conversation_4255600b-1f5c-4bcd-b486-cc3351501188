//
//  TransactionRefundVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/17.
//

import SwiftData
import SwiftUI

/// 交易退款视图模型
///
/// 负责交易退款的业务逻辑处理和数据管理，遵循新架构的MVVM模式。
/// 该类从DataManagement获取原始数据，进行业务逻辑处理后，
/// 为TransactionRefundView提供格式化的、可直接使用的数据。
///
/// ## 主要职责
/// - 退款金额验证和计算
/// - 退款卡片选择逻辑管理
/// - 原始交易信息格式化
/// - 退款操作执行和数据保存
///
/// ## 数据流向
/// ```
/// DataManagement → TransactionRefundVM → TransactionRefundView
/// ```
///
/// - Important: 使用DataManagement替代AppDataManager，确保数据一致性
/// - Note: 所有UI相关的数据处理都在ViewModel层完成
/// - Author: NZUE
/// - Version: 2.0 (New架构)
/// - Since: 2025.7.17
final class TransactionRefundVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供原始数据源
  ///
  /// 通过DataManagement获取卡片、交易、分类等基础数据，
  /// 作为所有业务逻辑计算的数据来源。
  private let dataManager: DataManagement

  /// 原始交易
  let transaction: TransactionModel

  // MARK: - Published Properties

  @Published var refundAmount: String = ""
  @Published var refundDate = Date()
  @Published var selectedCardId: UUID?
  @Published var errorMessage = ""
  @Published var showErrorAlert = false

  // MARK: - Computed Properties

  /// 原始分类名称
  var originalCategoryName: String {
    guard let categoryId = transaction.transactionCategoryId else {
      return "支出交易"
    }

    // 先尝试在子分类中查找
    for subCategory in dataManager.subCategories {
      if subCategory.id == categoryId {
        // 查找对应的主分类
        for mainCategory in dataManager.mainCategories {
          if mainCategory.id == subCategory.mainId {
            return "\(mainCategory.name)-\(subCategory.name)"
          }
        }
        return subCategory.name
      }
    }

    // 再在主分类中查找
    for mainCategory in dataManager.mainCategories {
      if mainCategory.id == categoryId {
        return mainCategory.name
      }
    }

    return "支出交易"
  }

  /// 原始卡片名称
  var originalCardName: String {
    guard let cardId = transaction.fromCardId else {
      return "未知卡片"
    }
    return dataManager.findCard(by: cardId)?.name ?? "未知卡片"
  }

  /// 分类图标信息
  var categoryIcon: IconType {
    guard let categoryId = transaction.transactionCategoryId else {
      return .emoji("❓")
    }

    // 使用DataManagement的getCategoryInfo方法
    let categoryInfo = dataManager.getCategoryInfo(for: categoryId)
    return categoryInfo.icon ?? .emoji("❓")
  }

  /// 是否可以保存
  var canSave: Bool {
    // 检查交易类型
    guard transaction.transactionType == .expense else { return false }

    // 检查退款金额是否有效
    guard let amount = Double(refundAmount), amount > 0 else { return false }

    // 检查退款金额是否超过可退金额
    let availableAmount = abs(transaction.transactionAmount) - (transaction.refundAmount ?? 0)
    guard amount <= availableAmount else { return false }

    // 检查是否选择了退款卡片
    guard selectedCardId != nil else { return false }

    return true
  }

  /// 可退款金额
  var availableRefundAmount: Double {
    abs(transaction.transactionAmount) - (transaction.refundAmount ?? 0)
  }

  /// 获取卡片信息
  func getCardInfo(for id: UUID?) -> (name: String, image: Data?) {
    guard let id = id else {
      return ("未知卡片", nil)
    }

    guard let card = dataManager.findCard(by: id) else {
      return ("未知卡片", nil)
    }

    return (card.name, card.bankLogo)
  }

  /// 获取卡片余额
  func getCardBalance(for id: UUID?) -> Double? {
    guard let id = id else { return nil }
    return dataManager.findCard(by: id)?.balance
  }

  // MARK: - Initialization

  /// 初始化交易退款视图模型
  ///
  /// 创建TransactionRefundVM实例并设置初始状态。
  /// 对于支出交易，默认选择原支出卡片作为退款接收卡片。
  ///
  /// - Parameters:
  ///   - transaction: 原始交易模型
  ///   - dataManager: 数据管理器，提供基础数据源
  ///
  /// - Note: 只有支出交易才能进行退款操作
  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.transaction = transaction
    self.dataManager = dataManager

    // 只允许支出交易退款，且初始选中的退款卡片为原支出卡片
    if transaction.transactionType == .expense {
      self.selectedCardId = transaction.fromCardId
    }
  }

  // MARK: - Public Methods

  /// 处理退款保存
  func handleRefundSave(modelContext: ModelContext) -> Bool {
    guard canSave,
      let refundAmountValue = Double(refundAmount),
      let refundCardId = selectedCardId,
      let refundCard = dataManager.findCard(by: refundCardId)
    else {
      errorMessage = "请选择退款接收卡片"
      showErrorAlert = true
      return false
    }

    do {
      // 计算退款交易的正确汇率
      let transactionCurrency = transaction.currency
      let refundCardCurrency = refundCard.currency
      let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode

      // 计算交易货币到退款卡片货币的汇率
      let refundIncomeToCardRate = CurrencyService.shared.getCurrencyRate(
        from: transactionCurrency,
        to: refundCardCurrency,
        currencies: dataManager.currencies
      )

      // 计算交易货币到基准货币的汇率
      let refundIncomeToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: transactionCurrency,
        to: baseCurrencyCode,
        currencies: dataManager.currencies
      )

      // 创建退款交易
      let refundTransaction = TransactionModel(
        id: UUID(),
        originalTransactionId: transaction.id,
        transactionType: .refund,
        transactionCategoryId: "SYS_REFUND",
        fromCardId: transaction.fromCardId,  // 从原始支出卡片退款
        toCardId: refundCardId,  // 退回到选择的卡片
        transactionAmount: refundAmountValue,
        currency: transaction.currency,
        symbol: transaction.symbol,
        expenseToCardRate: 1.0,  // 退款交易不涉及支出，设为1.0
        expenseToBaseRate: 1.0,  // 退款交易不涉及支出，设为1.0
        incomeToCardRate: refundIncomeToCardRate,  // 使用正确计算的汇率
        incomeToBaseRate: refundIncomeToBaseRate,  // 使用正确计算的汇率
        isStatistics: true,
        remark: "退款-\(originalCategoryName)",
        transactionDate: refundDate,
        createdAt: Date(),
        updatedAt: Date()
      )

      // 插入退款交易到数据库
      modelContext.insert(refundTransaction)

      // 更新原始交易的退款金额
      let currentRefundAmount = transaction.refundAmount ?? 0
      transaction.refundAmount = currentRefundAmount + refundAmountValue

      // 保存数据库更改
      try modelContext.save()

      // 重新计算相关卡片余额
      BalanceRecalculationService.shared.recalculateBalances(
        for: [refundTransaction.fromCardId, refundTransaction.toCardId],
        modelContext: modelContext,
        currencies: dataManager.currencies,
        operation: "退款创建"
      )

      return true

    } catch {
      errorMessage = "退款失败: \(error.localizedDescription)"
      showErrorAlert = true
      return false
    }
  }
}
