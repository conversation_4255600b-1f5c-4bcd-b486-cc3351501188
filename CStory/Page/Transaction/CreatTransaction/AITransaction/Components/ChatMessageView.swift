//
//  ChatMessageView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 聊天消息视图
///
/// 显示用户和AI之间的对话消息，支持文本、图片和交易结果的显示。
/// 用户消息显示在右侧，AI消息显示在左侧，并且可以点击交易结果查看详情。
///
/// ## 功能特性
/// - 区分用户和AI消息样式
/// - 支持图片网格显示
/// - 可点击的交易结果卡片
/// - 触觉反馈
struct ChatMessageView: View {
  // MARK: - 属性

  /// 消息模型
  let message: ChatMessageModel
  /// 所有交易数据
  let allTransactions: [TransactionModel]
  /// 卡片列表
  let cards: [CardModel]
  /// 主分类列表
  let mainCategories: [TransactionMainCategoryModel]
  /// 子分类列表
  let subCategories: [TransactionSubCategoryModel]
  /// 货币列表
  let currencies: [CurrencyModel]
  /// 交易列表
  let transactions: [TransactionModel]

  /// 路由管理器
  @EnvironmentObject private var pathManager: PathManagerHelper
  /// 点击的交易ID
  @State private var tappedTransactionId: UUID? = nil
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - 主体视图

  var body: some View {
    Group {
      if message.role == "user" {
        userMessageView
      } else {
        assistantMessageView
      }
    }
  }

  // MARK: - 子视图

  /// 用户消息视图
  private var userMessageView: some View {
    HStack {
      Spacer()
      VStack(alignment: .trailing, spacing: 8) {
        VStack(alignment: .trailing, spacing: 8) {
          // 文本内容
          if !message.content.isEmpty {
            Text(message.content)
              .padding(12)
              .background(Color.cAccentBlue)
              .foregroundColor(.cWhite)
              .cornerRadius(16)
          }

          // 图片内容
          if message.hasImages {
            ImageGridView(images: message.images)
          }
        }
      }
      .padding(.leading, 60)
    }
  }

  /// AI助手消息视图
  private var assistantMessageView: some View {
    HStack {
      VStack(alignment: .leading, spacing: 8) {
        // 文本内容
        if !message.content.isEmpty {
          Text(message.content)
            .padding(12)
            .background(Color.cLightBlue)
            .foregroundColor(.cBlack)
            .cornerRadius(16)
        }

        // 交易结果
        if message.hasTransactions {
          transactionResultsView
        }
      }
      .padding(.trailing, 60)
      Spacer()
    }
  }

  /// 交易结果视图
  private var transactionResultsView: some View {
    // 查找相关的交易
    let relatedTransactions = allTransactions.filter { transaction in
      message.transactionIds.contains(transaction.id)
    }

    return Group {
      if !relatedTransactions.isEmpty {
        VStack(alignment: .leading, spacing: 8) {
          // 成功提示
          HStack(spacing: 6) {
            Image(systemName: "checkmark.circle.fill")
              .foregroundColor(.green)
              .font(.system(size: 14))

            Text("已创建 \(relatedTransactions.count) 笔交易")
              .font(.system(size: 13, weight: .medium))
              .foregroundColor(.green)
          }
          .padding(.top, 4)

          // 交易列表
          ForEach(relatedTransactions, id: \.id) { transaction in
            let transactionRowVM = createTransactionRowVM(for: transaction)

            TransactionRow(viewModel: transactionRowVM)
              .background(Color.cWhite)
              .cornerRadius(16)
              .overlay(
                RoundedRectangle(cornerRadius: 16)
                  .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
              )
          }
        }
      }
    }
  }

  // MARK: - 辅助方法

  /// 创建TransactionRowVM
  private func createTransactionRowVM(for transaction: TransactionModel) -> TransactionRowVM {
    // 查找相关卡片
    let relatedCard = cards.first {
      $0.id == transaction.fromCardId || $0.id == transaction.toCardId
    }

    // 获取分类信息
    let categoryInfo = getCategoryInfo(for: transaction)

    // 创建TransactionRowVM
    return TransactionRowVM(
      transaction: transaction,
      relatedCard: relatedCard,  // AI聊天中显示卡片名称
      categoryInfo: categoryInfo,
      onTap: {
        hapticManager.trigger(.selection)
        pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
      }
    )
  }

  /// 获取交易分类信息
  private func getCategoryInfo(for transaction: TransactionModel) -> (name: String, icon: IconType?)
  {
    // 查找主分类
    if let mainCategory = mainCategories.first(where: { $0.id == transaction.transactionCategoryId }
    ) {
      return (name: mainCategory.name, icon: mainCategory.icon)
    }

    // 查找子分类
    if let subCategory = subCategories.first(where: { $0.id == transaction.transactionCategoryId })
    {
      return (name: subCategory.name, icon: subCategory.icon)
    }

    return (name: "未知类别", icon: nil)
  }
}
