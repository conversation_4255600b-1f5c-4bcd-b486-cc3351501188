//
//  AIMenuView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 菜单项模型
///
/// 定义AI聊天界面快捷菜单的单个选项
struct MenuItem {
  /// 图标名称（SF Symbol）
  let icon: String
  /// 菜单标题
  let title: String
  /// 点击动作
  let action: () -> Void
}

/// AI菜单视图
///
/// 在AI聊天界面底部显示的快捷菜单，提供相机、相册、导入、导出等快捷操作。
/// 支持动画显示/隐藏，并提供触觉反馈。
///
/// ## 使用示例
/// ```swift
/// AIMenuView(
///     menuItems: [
///         MenuItem(icon: "camera.fill", title: "相机", action: { ... }),
///         MenuItem(icon: "photo", title: "相册", action: { ... })
///     ],
///     showMenu: $showMenu
/// )
/// ```
struct AIMenuView: View {
  /// 菜单项数组
  let menuItems: [MenuItem]
  /// 控制菜单显示/隐藏
  @Binding var showMenu: Bool

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    HStack(spacing: 12) {
      // 遍历显示每个菜单项
      ForEach(menuItems, id: \.title) { item in
        Button(action: {
          hapticManager.trigger(.selection)
          item.action()
        }) {
          VStack(spacing: 4) {
            // 图标
            Image(systemName: item.icon)
              .font(.system(size: 22, weight: .regular))
              .foregroundColor(.cBlack)
              .frame(maxWidth: .infinity)
              .frame(height: 72)
              .background(Color.cWhite)
              .cornerRadius(12)

            // 标题
            Text(item.title)
              .font(.system(size: 13, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }
        .frame(maxWidth: .infinity)
      }
    }
    .padding(.horizontal, 12)
    .padding(.bottom, 12)
    // 动画过渡效果
    .transition(
      .asymmetric(
        insertion: .move(edge: .bottom).combined(with: .opacity),
        removal: .move(edge: .bottom).combined(with: .opacity)
      )
    )
  }
}
