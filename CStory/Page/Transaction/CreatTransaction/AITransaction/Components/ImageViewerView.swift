//
//  ImageViewerView.swift
//  CStory
//
//  Created by NZUE on 2025/7/11.
//

import SwiftUI

struct ImageViewerView: View {
    let images: [UIImage]
    @Binding var selectedIndex: Int
    @Binding var isPresented: Bool
    @GestureState private var magnifyBy = CGFloat(1.0)
    @State private var currentIndex: Int
    private let hapticManager = HapticFeedbackManager.shared
    
    init(images: [UIImage], selectedIndex: Binding<Int>, isPresented: Binding<Bool>) {
        self.images = images
        self._selectedIndex = selectedIndex
        self._isPresented = isPresented
        self._currentIndex = State(initialValue: selectedIndex.wrappedValue)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                TabView(selection: $currentIndex) {
                    ForEach(images.indices, id: \.self) { index in
                        Image(uiImage: images[index])
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .scaleEffect(magnifyBy)
                            .gesture(
                                MagnificationGesture()
                                    .updating($magnifyBy) { currentState, gestureState, _ in
                                        gestureState = currentState
                                    }
                            )
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        hapticManager.trigger(.impactLight)
                        selectedIndex = currentIndex
                        isPresented = false
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .principal) {
                    Text("\(currentIndex + 1) / \(images.count)")
                        .foregroundColor(.white)
                }
            }
        }
    }
}

// MARK: - 图片网格显示组件
struct ImageGridView: View {
    let images: [UIImage]
    let maxDisplay: Int = 9 // 最多显示9张
    @State private var showImageViewer = false
    @State private var selectedImageIndex = 0
    private let hapticManager = HapticFeedbackManager.shared
    
    private var displayImages: [UIImage] {
        Array(images.prefix(maxDisplay))
    }
    
    private var remainingCount: Int {
        max(0, images.count - maxDisplay)
    }
    
    // 根据图片数量动态调整单张图片大小
    private var imageSize: CGFloat {
        switch images.count {
        case 1:
            return 120 // 单张图片较大
        case 2:
            return 100 // 两张图片中等
        default:
            return 80  // 多张图片较小
        }
    }
    
    var body: some View {
        // 使用VStack垂直排列，像微信一样
        VStack(alignment: .trailing, spacing: 4) {
            ForEach(displayImages.indices, id: \.self) { index in
                ZStack {
                    Image(uiImage: displayImages[index])
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: imageSize, height: imageSize)
                        .clipped()
                        .cornerRadius(8)
                        .onTapGesture {
                            hapticManager.trigger(.selection)
                            selectedImageIndex = index
                            showImageViewer = true
                        }
                    
                    // 如果是最后一张且还有更多图片，显示剩余数量
                    if index == maxDisplay - 1 && remainingCount > 0 {
                        ZStack {
                            Color.black.opacity(0.6)
                                .cornerRadius(8)
                            
                            Text("+\(remainingCount)")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                        }
                        .onTapGesture {
                            hapticManager.trigger(.selection)
                            selectedImageIndex = index
                            showImageViewer = true
                        }
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showImageViewer) {
            ImageViewerView(
                images: images,
                selectedIndex: $selectedImageIndex,
                isPresented: $showImageViewer
            )
        }
    }
}