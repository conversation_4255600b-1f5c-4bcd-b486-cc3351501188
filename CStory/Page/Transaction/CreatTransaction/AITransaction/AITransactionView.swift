//
//  AITransactionView.swift
//  CStory
//
//  Created by NZUE on 2025/7/14.
//

import AVFoundation
import PhotosUI
import Speech
import SwiftData
import SwiftUI

/// AI记账视图
///
/// 专门处理AI智能记账的所有界面和逻辑，支持文本、语音、图片等
/// 多模态输入，自动识别交易信息并创建交易记录。
struct AITransactionView: View {
  // MARK: - Environment and Dependencies
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager

  // MARK: - ViewModel
  @StateObject private var viewModel = AITransactionViewModel()

  // MARK: - Animation Bindings
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - 回调函数
  var onSwitchToManual: (() -> Void)? = nil
  var onCloseRequested: (() -> Void)? = nil

  // MARK: - Focus State
  @FocusState private var isInputFocused: Bool

  // MARK: - Initialization

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onSwitchToManual: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onSwitchToManual = onSwitchToManual
    self.onCloseRequested = onCloseRequested
  }

  // MARK: - Local State (UI only)
  // 移除重复状态，现在都在 ViewModel 中管理

  var body: some View {
    GeometryReader { geometry in
      VStack(spacing: 0) {
        // AI 模式的控制栏
        topControlBar
          .offset(y: showTopContent ? 0 : -UIScreen.main.bounds.height)
          .zIndex(1)

        // AI消息列表视图 - 从上方滑入，动态高度
        messageListView
          .frame(maxHeight: .infinity)
          .offset(y: showTopContent ? 0 : -UIScreen.main.bounds.height)

        // AI输入区域视图 - 从下方滑入
        inputAreaView
          .offset(y: showBottomContent ? 0 : UIScreen.main.bounds.height)
      }
      .onAppear {
        // 设置依赖项
        viewModel.setupDependencies(dataManager: dataManager, modelContext: modelContext)
        setupAIView()
      }
      .overlay(
        // Toast 提示
        toastView,
        alignment: .top
      )
    }
    .onAppear {
      setupAIView()
    }
    .onDisappear {
      // 移除键盘观察者，避免内存泄漏
      NotificationCenter.default.removeObserver(
        self, name: UIResponder.keyboardWillShowNotification, object: nil)
      NotificationCenter.default.removeObserver(
        self, name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    .onChange(of: viewModel.selectedImages) { _, newValue in
      handleImageSelection(newValue)
    }
    .sheet(isPresented: $viewModel.showImagePicker) {
      ImagePicker(selectedImages: $viewModel.selectedImages)
    }
    .sheet(isPresented: $viewModel.showCamera) {
      CameraPicker(selectedImage: { image in
        viewModel.selectedImages.append(image)
      })
    }
    .alert(isPresented: $viewModel.showAlert) {
      permissionAlert
    }
  }

  // MARK: - View Components

  /// AI 模式的控制栏
  private var topControlBar: some View {
    HStack(spacing: 8) {
      // 清空聊天记录按钮
      Button(action: {
        hapticManager.trigger(.impactLight)
        clearChatHistory()
      }) {
        Text("清空")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(height: 28)
          .padding(.horizontal, 12)
          .background(Color.cWhite.opacity(0.9))
          .cornerRadius(14)
      }

      Spacer()

      // 切换到手动记账按钮
      Button(action: {
        hapticManager.trigger(.selection)
        onSwitchToManual?()
      }) {
        Text("手动记账")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(height: 28)
          .padding(.horizontal, 12)
          .background(Color.cWhite.opacity(0.9))
          .cornerRadius(14)
      }

      // 关闭按钮
      Button(action: {
        hapticManager.trigger(.impactLight)
        onCloseRequested?()
      }) {
        Image(systemName: "xmark")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(width: 28, height: 28)
          .background(Color.cWhite.opacity(0.9))
          .cornerRadius(14)
      }
    }
    .padding(.horizontal, 12)
    .padding(.vertical, 8)

  }

  private var messageListView: some View {
    ScrollView {
      LazyVStack(spacing: 12) {
        ForEach(viewModel.messages, id: \.id) { message in
          ChatMessageView(
            message: message,
            allTransactions: dataManager.allTransactions,
            cards: dataManager.cards,
            mainCategories: dataManager.mainCategories,
            subCategories: dataManager.subCategories,
            currencies: dataManager.currencies,
            transactions: dataManager.allTransactions
          )
        }

        // 录音状态显示
        if viewModel.isRecording {
          recordingPreview
        }

        // 加载状态显示
        if viewModel.isLoading {
          loadingIndicatorView
        }
      }
      .padding(.horizontal)
      .padding(.vertical, 12)
    }
    .scrollPosition($viewModel.scrollPosition)

    .onAppear {
      setupKeyboardObservers()
      // 延迟滚动，等待初始动画完成
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        scrollToBottom()
      }
      scrollToBottom()
    }
    .onChange(of: viewModel.messages.count) { _, _ in
      scrollToBottom()
    }
    .onChange(of: viewModel.keyboardHeight) { _, _ in
      // 键盘高度变化时重新滚动到底部
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        scrollToBottom()
      }
    }
    .onChange(of: viewModel.showMenu) { _, _ in
      // 菜单显示/隐藏时重新滚动到底部，等待动画完成
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        scrollToBottom()
      }
    }
    .onChange(of: viewModel.selectedImages.count) { _, _ in
      // 图片数量变化时重新滚动到底部
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        scrollToBottom()
      }
    }
  }

  // MARK: - 滚动辅助方法

  /// 滚动到底部的统一方法 (使用 iOS 17+ scrollPosition)
  private func scrollToBottom() {
    // 使用更短的动画时间，让滚动更快响应布局变化
    withAnimation(.easeOut(duration: 0.2)) {
      viewModel.scrollPosition.scrollTo(edge: .bottom)
    }
  }

  /// 显示 Toast 消息
  private func showToastMessage(_ message: String) {
    viewModel.showToast(message: message)
  }

  /// Toast 视图
  private var toastView: some View {
    Group {
      if viewModel.showToast {
        Text(viewModel.toastMessage)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cWhite)
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
          .background(.cBlack.opacity(0.8))
          .cornerRadius(20)
          .padding(.top, 60)
          .transition(.move(edge: .top).combined(with: .opacity))
      }
    }
  }

  /// 设置键盘观察者
  private func setupKeyboardObservers() {
    NotificationCenter.default.addObserver(
      forName: UIResponder.keyboardWillShowNotification,
      object: nil,
      queue: .main
    ) { notification in
      if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey]
        as? CGRect
      {
        viewModel.keyboardHeight = keyboardFrame.height
      }
    }

    NotificationCenter.default.addObserver(
      forName: UIResponder.keyboardWillHideNotification,
      object: nil,
      queue: .main
    ) { _ in
      viewModel.keyboardHeight = 0
    }
  }

  private var inputAreaView: some View {

    VStack(spacing: 8) {
      recordingStatusView

      VStack(spacing: 12) {
        // 图片展示区域
        ImageGalleryView(
          selectedImages: $viewModel.selectedImages,
          showImagePicker: $viewModel.showImagePicker
        )
        .disabled(viewModel.isLoading)

        // 输入区域
        inputContainerView

        // 菜单
        if viewModel.showMenu {
          AIMenuView(
            menuItems: menuItems,
            showMenu: $viewModel.showMenu
          )
        }
      }
      .disabled(viewModel.isLoading)
      .opacity(viewModel.isLoading ? 0.6 : 1.0)

      .background(.cBlack.opacity(0.05))

      .cornerRadius(24)
    }
    .padding(12)
    .onTapGesture {
      // 加载中点击显示 Toast
      if viewModel.isLoading {
        showToastMessage("正在处理中，请稍候...")
      }
    }

  }

  // MARK: - AI功能视图组件

  /// 加载指示器视图 - 与 assistantMessageView 样式完全一致
  var loadingIndicatorView: some View {
    HStack {
      VStack(alignment: .leading, spacing: 8) {
        // 加载内容 - 与 AI 消息样式一致
        HStack(spacing: 8) {
          ProgressView()
            .scaleEffect(0.8)
            .progressViewStyle(CircularProgressViewStyle(tint: .cBlack))

          Text("AI 正在思考中...")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
        }
        .padding(12)
        .background(Color.cLightBlue)
        .foregroundColor(.cBlack)
        .cornerRadius(16)
      }
      .padding(.trailing, 60)
      Spacer()
    }
    .transition(
      .asymmetric(
        insertion: .move(edge: .leading).combined(with: .opacity),
        removal: .move(edge: .leading).combined(with: .opacity)
      ))
  }

  var recordingPreview: some View {
    HStack {
      Spacer()
      VStack(alignment: .trailing, spacing: 8) {
        Text("正在录音...")
          .font(.caption)
          .foregroundColor(.gray)

        Text(viewModel.userInput)
          .padding(12)
          .background(Color.cAccentBlue.opacity(0.6))
          .foregroundColor(.cWhite)
          .cornerRadius(16)
      }
      .padding(.leading, 60)
    }
    .opacity(0.6)
  }

  var recordingStatusView: some View {
    Group {
      if viewModel.isRecording {
        if viewModel.isOutOfBounds {
          Text("松手取消")
            .font(.system(size: 13, weight: .regular))
            .foregroundColor(.cAccentRed)
        } else {
          Text("松手发送，上移取消")
            .font(.system(size: 13, weight: .regular))
            .foregroundColor(.cAccentBlue)
        }
      }
    }
  }

  var inputContainerView: some View {
    ZStack {
      // 录音波形
      if viewModel.isRecording {
        AudioWaveformView(audioLevel: 0.5)  // 简化为固定值或从 ViewModel 获取
          .padding(.horizontal, 20)
      }

      // 输入控件
      HStack(alignment: .bottom, spacing: 8) {
        leftButtonsView
        centerInputView
        rightButtonsView
      }
      .opacity(viewModel.isRecording ? 0.001 : 1)
    }
    .padding(12)
    .background(
      viewModel.isRecording
        ? (viewModel.isOutOfBounds ? Color.cAccentRed : Color.cAccentBlue) : .clear
    )
    .background(Color.cWhite)
    .cornerRadius(24)
  }

  var leftButtonsView: some View {
    Group {
      if !isInputFocused && viewModel.userInput.isEmpty {
        Button(action: {
          hapticManager.trigger(.selection)
          if viewModel.checkCameraPermission() {
            viewModel.showCamera = true
          }
        }) {
          Image(systemName: "camera")
            .font(.system(size: 22, weight: .regular))
            .frame(width: 32, height: 32)
            .foregroundColor(.cBlack)
        }
      }
    }
  }

  var centerInputView: some View {
    Group {
      if viewModel.showKeyboard {
        TextField("发消息...", text: $viewModel.userInput, axis: .vertical)
          .font(.system(size: 16, weight: .regular))
          .foregroundColor(.cBlack)
          .frame(minHeight: 32)
          .lineLimit(1...10)
          .focused($isInputFocused)
          .onChange(of: isInputFocused) { _, newValue in
            if newValue && viewModel.showMenu {

              withAnimation(.easeInOut(duration: 0.25)) {
                viewModel.showMenu = false
              }
            }
          }
      } else {
        Text("按住说话")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)
          .frame(maxWidth: .infinity)
          .frame(minHeight: 32)
          .background(.white.opacity(0.001))
          .gesture(voiceRecordingGesture)
      }
    }
  }

  var rightButtonsView: some View {
    HStack(spacing: 8) {
      // 键盘/语音切换按钮
      if viewModel.userInput.isEmpty {
        Button(action: {
          hapticManager.trigger(.selection)
          toggleInputMode()
        }) {
          Image(systemName: viewModel.showKeyboard ? "wave.3.right.circle" : "keyboard")
            .font(.system(size: 24, weight: .regular))
            .frame(width: 32, height: 32)
            .foregroundColor(.cBlack)
            .animation(.none, value: viewModel.showKeyboard)
        }
        .transition(.scale.combined(with: .opacity))
      }

      // 发送按钮
      if !viewModel.userInput.isEmpty || !viewModel.selectedImages.isEmpty {
        Button(action: {
          hapticManager.trigger(.impactMedium)
          sendMessage()
        }) {
          Image(systemName: "arrow.up.circle")
            .font(.system(size: 24, weight: .regular))
            .frame(width: 32, height: 32)
            .foregroundColor(.cAccentBlue)
        }
        .transition(.scale.combined(with: .opacity))
      }

      // 菜单按钮
      if viewModel.userInput.isEmpty && viewModel.selectedImages.isEmpty {
        Button(action: {
          hapticManager.trigger(.selection)
          toggleMenu()
        }) {
          Image(systemName: "plus.circle")
            .font(.system(size: 24, weight: .regular))
            .frame(width: 32, height: 32)
            .foregroundColor(.cBlack)
            .rotationEffect(.degrees(viewModel.showMenu ? 45 : 0))
            .animation(.easeInOut(duration: 0.25), value: viewModel.showMenu)
        }
        .transition(.scale.combined(with: .opacity))
      }
    }
  }

  var permissionAlert: Alert {
    Alert(
      title: Text("权限需要"),
      message: Text(viewModel.alertMessage),
      primaryButton: .default(Text("设置")) {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
          UIApplication.shared.open(settingsUrl)
        }
      },
      secondaryButton: .cancel(Text("取消"))
    )
  }

  // MARK: - AI功能计算属性

  var menuItems: [MenuItem] {
    [
      MenuItem(
        icon: "camera.fill", title: "相机",
        action: {
          if viewModel.checkCameraPermission() {
            viewModel.showCamera = true
          }
        }),
      MenuItem(
        icon: "photo", title: "相册",
        action: {
          if viewModel.checkPhotoPermission() {
            viewModel.showImagePicker = true
          }
        }),
      MenuItem(icon: "arrow.up.document.fill", title: "导入", action: { print("导入") }),
      MenuItem(icon: "arrow.down.document.fill", title: "导出", action: { print("导出") }),
    ]
  }

  var voiceRecordingGesture: some Gesture {
    DragGesture(minimumDistance: 0)
      .onChanged { value in
        if !viewModel.showKeyboard {
          if !viewModel.isRecording {
            viewModel.startRecording()
          } else {
            viewModel.updateRecordingBounds(value.location, screenBounds: UIScreen.main.bounds)
          }
        }
      }
      .onEnded { _ in
        if !viewModel.showKeyboard {
          if viewModel.isOutOfBounds {
            viewModel.cancelRecording()
          } else {
            viewModel.stopRecording()
          }
        }
      }
  }

  // MARK: - AI功能方法

  func setupAIView() {
    loadChatHistory()

    Task {
      await viewModel.requestAllPermissions()
    }
  }

  func handleImageSelection(_ newValue: [UIImage]) {
    if !newValue.isEmpty && viewModel.showMenu {
      withAnimation(.easeInOut(duration: 0.25)) {
        viewModel.showMenu = false
      }
    }
  }

  func toggleInputMode() {
    withAnimation(.easeInOut(duration: 0.25)) {
      if viewModel.showKeyboard {
        isInputFocused = false
      }
      // 移除自动聚焦，让用户手动点击输入框
      viewModel.showKeyboard.toggle()
    }
  }

  func toggleMenu() {
    if isInputFocused {
      isInputFocused = false
    }
    withAnimation(.easeInOut(duration: 0.25)) {
      viewModel.showMenu.toggle()
    }
  }

  // 录音相关方法已移到 ViewModel 中

  func loadChatHistory() {
    if let chatService = viewModel.chatService {
      viewModel.messages = chatService.getRecentMessages(limit: 100)
    }
  }

  func clearChatHistory() {
    viewModel.clearMessages()
  }

  // MARK: - AI消息处理

  func sendMessage(_ text: String? = nil) {
    if let text = text {
      viewModel.userInput = text
    }

    // 检查是否正在加载中
    if viewModel.isLoading {
      viewModel.showToast(message: "正在处理中，请稍候...")
      return
    }

    // 清空输入时确保焦点也被清除
    isInputFocused = false

    // 调用 ViewModel 的发送方法
    viewModel.sendMessage()

    // 旧的实现已移到 ViewModel 中
  }

  func handleAIResponse(_ response: String) -> [TransactionModel]? {
    guard let data = response.data(using: .utf8),
      let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
      let transactionsData = json["transactions"] as? [[String: Any]]
    else { return nil }

    // 处理空数组的情况
    if transactionsData.isEmpty {
      print("✅ AI 返回空交易数组，无需创建交易")
      return []
    }

    return createTransactionsFromJSON(transactionsData)
  }

  // 权限管理方法已移到 ViewModel 中

  // MARK: - AI交易创建

  func createTransactionsFromJSON(_ transactionsData: [[String: Any]]) -> [TransactionModel]? {
    // 处理空数组的情况
    if transactionsData.isEmpty {
      print("✅ 交易数据为空数组，返回空结果")
      return []
    }

    var createdTransactions: [TransactionModel] = []
    var affectedCardIds: [UUID?] = []

    for transactionData in transactionsData {
      guard let transactionTypeString = transactionData["transactionType"] as? String,
        let transactionType = TransactionType(rawValue: transactionTypeString),
        let amount = transactionData["amount"] as? Double,
        amount > 0
      else {
        continue
      }

      let transactionDate: Date
      if let timestamp = transactionData["timestamp"] as? Int, timestamp > 0 {
        transactionDate = Date(timeIntervalSince1970: TimeInterval(timestamp))
      } else {
        transactionDate = Date()
      }

      let categoryId = transactionData["categoryId"] as? String
      let cardId: UUID?
      if let cardIdString = transactionData["cardId"] as? String, !cardIdString.isEmpty {
        cardId = UUID(uuidString: cardIdString)
      } else {
        cardId = nil
      }

      let note = transactionData["note"] as? String ?? ""
      let currency = transactionData["currency"] as? String ?? "CNY"
      let discount: Double =
        transactionType == .expense ? (transactionData["discount"] as? Double ?? 0.0) : 0.0

      let transaction = createTransaction(
        type: transactionType,
        amount: amount,
        categoryId: categoryId,
        cardId: cardId,
        currency: currency,
        note: note,
        date: transactionDate,
        discount: discount
      )

      if let transaction = transaction {
        createdTransactions.append(transaction)
        affectedCardIds.append(transaction.fromCardId)
        affectedCardIds.append(transaction.toCardId)
      }
    }

    if !createdTransactions.isEmpty {
      saveTransactions(createdTransactions, affectedCardIds: affectedCardIds)
      return createdTransactions
    }

    return nil
  }

  private func createTransaction(
    type: TransactionType,
    amount: Double,
    categoryId: String?,
    cardId: UUID?,
    currency: String,
    note: String,
    date: Date,
    discount: Double = 0.0
  ) -> TransactionModel? {

    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    let transactionCurrency = dataManager.currencies.first { $0.code == currency }
    let baseCurrency = dataManager.currencies.first { $0.code == baseCurrencyCode }
    let currencySymbol = transactionCurrency?.symbol ?? baseCurrency?.symbol ?? "¥"

    switch type {
    case .expense:
      let fromCardId = cardId ?? dataManager.cards.first?.id
      let fromCard = dataManager.cards.first(where: { $0.id == fromCardId }) ?? dataManager.cards.first
      let cardCurrency = fromCard?.currency ?? baseCurrencyCode
      let expenseToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: categoryId,
        fromCardId: fromCardId,
        toCardId: nil,
        discountAmount: discount > 0 ? discount : nil,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    case .income:
      let toCardId = cardId ?? dataManager.cards.first?.id
      let toCard = dataManager.cards.first(where: { $0.id == toCardId }) ?? dataManager.cards.first
      let cardCurrency = toCard?.currency ?? baseCurrencyCode
      let incomeToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let incomeToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .income,
        transactionCategoryId: categoryId,
        fromCardId: nil,
        toCardId: toCardId,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: incomeToCardRate,
        incomeToBaseRate: incomeToBaseRate,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    case .transfer:
      let cardUUID = cardId ?? dataManager.cards.first?.id
      let card = dataManager.cards.first(where: { $0.id == cardUUID }) ?? dataManager.cards.first
      let cardCurrency = card?.currency ?? baseCurrencyCode
      let expenseToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .transfer,
        transactionCategoryId: "SYS_TRANSFER",
        fromCardId: cardUUID,
        toCardId: cardUUID,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: expenseToCardRate,
        incomeToBaseRate: expenseToBaseRate,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    default:
      let card = dataManager.cards.first(where: { $0.id == cardId })
      let cardCurrency = card?.currency ?? baseCurrencyCode
      let toCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let toBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: type,
        transactionCategoryId: categoryId,
        fromCardId: type == .refund ? nil : cardId,
        toCardId: type == .refund ? cardId : nil,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: type == .refund ? 1.0 : toCardRate,
        expenseToBaseRate: type == .refund ? 1.0 : toBaseRate,
        incomeToCardRate: type == .refund ? toCardRate : 1.0,
        incomeToBaseRate: type == .refund ? toBaseRate : 1.0,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )
    }
  }

  private func saveTransactions(_ transactions: [TransactionModel], affectedCardIds: [UUID?]) {
    do {
      for transaction in transactions {
        modelContext.insert(transaction)
      }

      try modelContext.save()

      BalanceRecalculationService.shared.recalculateBalances(
        for: affectedCardIds,
        modelContext: modelContext,
        currencies: dataManager.currencies,
        operation: "AI交易创建"
      )

    } catch {
      viewModel.showAlert = true
      viewModel.alertMessage = "保存交易记录失败: \(error.localizedDescription)"
    }
  }
}

#Preview {
  @Previewable @State var showTopContent = true
  @Previewable @State var showBottomContent = true
  @Previewable @State var hapticTrigger = false

  AITransactionView(
    showTopContent: $showTopContent,
    showBottomContent: $showBottomContent
  )
  .environment(\.dataManager, DataManagement(
    cards: [],
    mainCategories: [],
    subCategories: [],
    currencies: [],
    recentTransactions: [],
    allTransactions: []
  ))
  .modelContainer(try! ModelContainer(for: TransactionModel.self))
}
