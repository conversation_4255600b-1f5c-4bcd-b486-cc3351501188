//
//  TransactionDetailView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 交易详情视图
///
/// 显示交易的详细信息，包括金额、分类、时间、备注等。
/// 支持编辑模式，可修改交易信息并保存。
///
/// ## 功能特性
/// - 查看交易详细信息
/// - 编辑交易各项属性
/// - 删除交易记录
/// - 退款操作支持
/// - 实时汇率转换显示
struct TransactionDetailView: View {
  // MARK: - 属性

  let transaction: TransactionModel

  // MARK: - 环境属性

  @Environment(\.modelContext) private var modelContext
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 状态属性

  // 使用 ObservedObject 来管理 ViewModel
  @ObservedObject private var viewModel: TransactionDetailViewModel

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.transaction = transaction
    self.viewModel = TransactionDetailViewModel(
      transaction: transaction,
      dataManager: dataManager
    )
  }

  /// 顶部区域视图
  ///
  /// 包含交易标题、时间、状态标签和备注信息
  private var topArea: some View {
    VStack(spacing: 8) {
      /// 交易标题
      HStack(spacing: 12) {
        Text(viewModel.detailData?.transactionTitle ?? "加载中...")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.cBlack)
          .frame(maxWidth: .infinity, alignment: .leading)

        if let data = viewModel.detailData {
          if data.hasRefund {
            HStack(spacing: 4) {
              Circle()
                .frame(width: 6, height: 6)
                .foregroundColor(.cAccentGreen)
              Text("退款")
                .font(.system(size: 13))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }

          if data.hasDiscount {
            HStack(spacing: 4) {
              Circle()
                .frame(width: 6, height: 6)
                .foregroundColor(.orange)
              Text("优惠")
                .font(.system(size: 13))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }
        }
      }
      /// 交易时间
      HStack(spacing: 12) {
        Text(getCurrentDisplayData()?.formattedDate ?? "加载中...")
          .font(.system(size: 15))
          .foregroundColor(.cBlack.opacity(0.6))

        if viewModel.isEditing {
          Button(action: {
            hapticManager.trigger(.selection)
            viewModel.showTimeSheet = true
          }) {
            Image("edit_icon")
              .font(.system(size: 16))
              .foregroundColor(.cAccentBlue)
          }
          .transition(.move(edge: .trailing).combined(with: .opacity))
        }

        Spacer()
      }

      // 备注显示区域
      if viewModel.isEditing || !transaction.remark.isEmpty {
        HStack(alignment: .center, spacing: 8) {
          Text("备注：")
            .font(.system(size: 15))
            .foregroundColor(.cBlack.opacity(0.6))

          if viewModel.isEditing {
            TextField(
              "添加备注",
              text: Binding(
                get: { viewModel.editingData?.note ?? transaction.remark },
                set: { viewModel.editingData?.note = $0 }
              )
            )
            .font(.system(size: 15))
            .padding(8)
            .background(Color.cAccentBlue.opacity(0.05))
            .cornerRadius(8)
          } else {
            Text(transaction.remark)
              .font(.system(size: 15))
              .foregroundColor(.cBlack.opacity(0.6))
          }
          Spacer()
        }
        .padding(.top, 8)
      }
    }
    .padding(.vertical, 24)
    .padding(.horizontal, 16)
  }

  ///交易明细
  private var transactionDetail: some View {
    VStack(spacing: 12) {
      if let data = getCurrentDisplayData() {
        // 根据交易类型显示不同的编辑选项
        if transaction.transactionType == .refund {
          // 退款交易：可以编辑退款金额
          DetailRowView(
            title: "退款金额",
            currencySymbol: data.currencySymbol,
            amount: data.subtotal,
            isTotal: false,
            isEditing: viewModel.isEditing,
            onEditTap: {
              hapticManager.trigger(.selection)
              viewModel.openNumericKeypad(for: .amount)
            },
          )
        } else {
          // 原始交易：正常的小计、优惠、退款、合计显示
          DetailRowView(
            title: data.hasDiscount || data.hasRefund ? "小计" : "总计",
            currencySymbol: data.currencySymbol,
            amount: data.subtotal,
            isTotal: !(data.hasDiscount || data.hasRefund),
            isEditing: viewModel.isEditing,
            onEditTap: {
              hapticManager.trigger(.selection)
              viewModel.openNumericKeypad(for: .amount)
            },
          )

          if data.discountAmount > 0
            || (viewModel.isEditing
              && transaction.transactionType == .expense)
          {
            DetailRowView(
              title: "优惠",
              currencySymbol: data.currencySymbol,
              amount: data.discountAmount,
              isTotal: false,
              isEditing: viewModel.isEditing,
              onEditTap: {
                hapticManager.trigger(.selection)
                viewModel.openNumericKeypad(for: .discount)
              },
            )
          }

          // 显示汇总的退款金额
          if data.refundAmount > 0 {
            DetailRowView(
              title: "已退款",
              currencySymbol: data.currencySymbol,
              amount: data.refundAmount,
              isTotal: false,
              isEditing: false,  // 退款金额不可编辑，应通过退款交易管理
              onEditTap: nil,
            )
          }

          // 只有在有优惠或退款时才显示合计
          if data.hasDiscount || data.hasRefund {
            DetailRowView(
              title: "合计",
              currencySymbol: data.currencySymbol,
              amount: data.totalAmount,
              isTotal: true,
              isEditing: false,  // 合计不可编辑
              onEditTap: nil,
            )
          }
        }
      } else {
        // 加载状态
        ForEach(0..<4, id: \.self) { _ in
          DetailRowView(
            title: "加载中...", amount: "¥0.00", isTotal: false, isEditing: false, onEditTap: nil,
          )
        }
      }
    }
    .padding(.top, 12)
    .padding(.horizontal, 24)
  }

  // MARK: - 详情行组件
  private struct DetailRowView: View {
    let title: String
    let amount: String?
    let currencySymbol: String?
    let amountValue: Double?
    let isTotal: Bool
    let isEditing: Bool
    let onEditTap: (() -> Void)?

    // MARK: - 触觉反馈管理器
    private let hapticManager = HapticFeedbackManager.shared

    // 字符串金额初始化（向后兼容）
    init(
      title: String, amount: String, isTotal: Bool = false, isEditing: Bool = false,
      onEditTap: (() -> Void)? = nil
    ) {
      self.title = title
      self.amount = amount
      self.currencySymbol = nil
      self.amountValue = nil
      self.isTotal = isTotal
      self.isEditing = isEditing
      self.onEditTap = onEditTap
    }

    // 货币符号和金额分离初始化（新增）
    init(
      title: String, currencySymbol: String, amount: Double, isTotal: Bool = false,
      isEditing: Bool = false,
      onEditTap: (() -> Void)? = nil
    ) {
      self.title = title
      self.amount = nil
      self.currencySymbol = currencySymbol
      self.amountValue = amount
      self.isTotal = isTotal
      self.isEditing = isEditing
      self.onEditTap = onEditTap
    }

    var body: some View {
      HStack(spacing: 0) {
        Text(title)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.4))
        Spacer()
        HStack {
          // 根据初始化方式显示金额
          if let amount = amount {
            // 使用字符串金额（向后兼容）
            Text(amount)
              .font(.system(size: isTotal ? 18 : 15, weight: .medium))
              .foregroundColor(isTotal ? Color.cBlack : Color.cBlack.opacity(0.8))
          } else if let currencySymbol = currencySymbol, let amountValue = amountValue {
            // 使用 DisplayCurrencyView 正确显示负数
            if isTotal {
              DisplayCurrencyView.size18(symbol: currencySymbol, amount: amountValue)
                .simpleFormat()
                .color(Color.cBlack)
            } else {
              DisplayCurrencyView.size15(symbol: currencySymbol, amount: amountValue)
                .simpleFormat()
                .color(Color.cBlack.opacity(0.8))
            }
          }

          // 在编辑模式下，对于有编辑回调的字段都显示编辑按钮
          if isEditing && onEditTap != nil {
            Button(action: {
              onEditTap?()
            }) {
              Image("edit_icon")
                .font(.system(size: 16))
                .foregroundColor(.cAccentBlue)
            }
            .transition(.move(edge: .trailing).combined(with: .opacity))
          }
        }
      }
      .contentShape(Rectangle())
      .onTapGesture {
        if isEditing {
          hapticManager.trigger(.selection)
          onEditTap?()
        }
      }
    }
  }

  /// 操作按钮区域
  private var actionButtonSection: some View {
    VStack {
      Spacer()
      ZStack {
        VariableBlurView(maxBlurRadius: 15, direction: .blurredBottomClearTop, startOffset: -0.05)
          .ignoresSafeArea()
          .frame(height: 64)

        ActionButtonsView(
          isEditing: $viewModel.isEditing,
          transactionType: transaction.transactionType,
          canRefund: viewModel.canRefund,
          onRefund: {
            handleRefundAction()
          },
          onEdit: {
            // 进入编辑模式
            viewModel.enterEditingMode()
          },
          onCancel: {
            // 取消编辑
            viewModel.cancelEditing()
          },
          onSave: {
            handleSaveAction()
          }
        )
        .padding(.vertical, 12)
      }
    }
  }
  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        /// 导航栏
        NavigationBarKit(
          viewModel: NavigationBarKitVM(
            title: "交易详情",
            backAction: {
              hapticManager.trigger(.selection)
              dismiss()
            },
            rightButton: .icon(
              "trash",
              style: .destructive,
              action: {
                hapticManager.trigger(.selection)
                if !viewModel.isEditing {  // 编辑模式下禁用删除按钮
                  viewModel.showDeleteAlert = true
                }
              }
            )
          )
        )
        .font(.system(size: 15, weight: .medium))
        .foregroundColor(.cBlack)

        /// 顶部区域
        topArea
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {
            DividerTitleKit(title: "交易类别")
              .padding(.horizontal, 16)
            /// 交易类别
            HStack(spacing: 12) {
              // 使用IconView来正确显示图标（包括图片）
              let categoryInfo = viewModel.getCategoryInfo(
                from: viewModel.isEditing
                  ? viewModel.editingData?.categoryId : transaction.transactionCategoryId,
                transactionType: transaction.transactionType,
                categories: dataManager.mainCategories,
                subCategories: dataManager.subCategories
              )
              IconView(
                viewModel: IconViewVM(
                  icon: categoryInfo.icon,
                  size: 40,
                  fontSize: 20,
                  backgroundColor: Color.clear,
                  cornerRadius: 12
                ))
              Text(getCurrentDisplayData()?.categoryName ?? "加载中...")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)
              Spacer()
              //               退款交易不允许编辑分类，分类应与原始交易保持一致
              if viewModel.isEditing && transaction.transactionType != .refund {
                ActionButton(
                  viewModel: ActionButtonVM.customColor(
                    title: "编辑类别",
                    action: {
                      hapticManager.trigger(.selection)
                      viewModel.showCategorySheet = true
                    },
                    textColor: Color.cAccentBlue,
                    strokeColor: Color.cAccentBlue.opacity(0.08)
                  )
                )
                .transition(.move(edge: .trailing).combined(with: .opacity))
              }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .onTapGesture {
              if viewModel.isEditing && transaction.transactionType != .refund {
                hapticManager.trigger(.selection)
                viewModel.showCategorySheet = true
              }
            }

            DividerKit.dashed()
              .padding(.horizontal, 24)
            ///交易明细
            transactionDetail

            // 汇率信息（仅在非本位币时显示）
            if let data = getCurrentDisplayData(),
              let exchangeRate = data.exchangeRateInfo,
              !exchangeRate.isBaseCurrency
            {
              DividerTitleKit(title: "汇率信息")
                .padding(.horizontal, 16)

              // 汇率显示
              HStack(spacing: 0) {
                Text("汇率")
                  .font(.system(size: 14, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.4))

                Spacer()
                Text(exchangeRate.formattedRate)
                  .font(.system(size: 15, weight: .medium))
                  .foregroundColor(.cBlack)
              }
              .padding(.horizontal, 24)
            }

            DividerTitleKit(title: "卡片详情")
              .padding(.horizontal, 16)

            // 根据卡片显示模式显示不同的布局
            if let data = getCurrentDisplayData() {
              switch data.cardDisplayMode {
              case .single(let card, let type):
                // 单卡片显示
                SingleCardView(
                  card: card,
                  cardType: type,
                  isEditing: viewModel.isEditing,
                  onEditCard: {
                    hapticManager.trigger(.selection)
                    // 根据卡片类型设置编辑类型
                    let cardType: TransactionDetailViewModel.CardEditType =
                      (type.contains("支出") || type.contains("转出")) ? .from : .to
                    viewModel.currentEditingCardType = cardType
                    viewModel.showCardSheet = true
                  },
                )
              case .transfer(let fromCard, let toCard):
                // 转账双卡片显示
                TransferCardsView(
                  fromCard: fromCard,
                  toCard: toCard,
                  isEditing: viewModel.isEditing,
                  onEditFromCard: {
                    hapticManager.trigger(.selection)
                    viewModel.currentEditingCardType = .from
                    viewModel.showCardSheet = true
                  },
                  onEditToCard: {
                    hapticManager.trigger(.selection)
                    viewModel.currentEditingCardType = .to
                    viewModel.showCardSheet = true
                  },
                )
              }
            } else {
              // 加载状态
              SingleCardView(
                card: nil,
                cardType: "加载中...",
                isEditing: false,
                onEditCard: nil,
              )
            }

            // 只有在有退款记录时才显示退款记录区域
            if let data = viewModel.detailData, !data.refundTransactions.isEmpty {
              DividerTitleKit(title: "退款记录")
                .padding(.horizontal, 16)

              ForEach(data.refundTransactions, id: \.id) { refundTransaction in
                /// 退款记录（添加导航功能）
                Button(action: {
                  hapticManager.trigger(.selection)
                  pathManager.path.append(
                    NavigationDestination.transactionDetailView(refundTransaction.id))
                }) {
                  HStack(spacing: 12) {
                    // 显示退回账户的logo
                    if let refundCard = dataManager.cards.first(where: {
                      $0.id == refundTransaction.toCardId
                    }) {
                      if let imageData = refundCard.bankLogo,
                        let uiImage = UIImage(data: imageData)
                      {
                        Image(uiImage: uiImage)
                          .resizable()
                          .frame(width: 40, height: 40)
                          .cornerRadius(12)
                      } else {
                        IconView(
                          viewModel: IconViewVM(
                            icon: IconType.emoji("❓"),
                            size: 40,
                            fontSize: 32,
                            backgroundColor: Color.cAccentBlue.opacity(0.1)
                          ))
                      }
                    } else {
                      IconView(
                        viewModel: IconViewVM(
                          icon: IconType.emoji("❓"),
                          size: 40,
                          fontSize: 32,
                          backgroundColor: Color.cAccentBlue.opacity(0.1)
                        ))
                    }
                    VStack(alignment: .leading, spacing: 4) {
                      if let refundCard = dataManager.cards.first(where: {
                        $0.id == refundTransaction.toCardId
                      }) {
                        Text(refundCard.name)
                          .font(.system(size: 14, weight: .medium))
                          .foregroundColor(.cBlack)
                      } else {
                        Text("未知卡片")
                          .font(.system(size: 14, weight: .medium))
                          .foregroundColor(.cBlack)
                      }
                      Text("退回卡片")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(.cBlack.opacity(0.6))
                    }
                    Spacer()
                    VStack(alignment: .trailing, spacing: 4) {
                      Text(
                        "\(refundTransaction.symbol)\(NumberFormatService.shared.formatAmount(refundTransaction.transactionAmount))"
                      )
                      .font(.system(size: 14, weight: .medium))
                      .foregroundColor(.cBlack)
                      Text(
                        DateFormattingHelper.shared.smartFormat(
                          date: refundTransaction.transactionDate)
                      )
                      .font(.system(size: 13, weight: .medium))
                      .foregroundColor(.cBlack.opacity(0.6))
                    }
                  }
                  .padding(.horizontal, 24)
                  .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
              }
            }

            // 如果当前是退款交易，显示原始交易信息
            if transaction.transactionType == .refund,
              let originalId = transaction.originalTradId,
              let originalTransaction = dataManager.allTransactions.first(where: {
                $0.id == originalId
              }
              )
            {

              DividerTitleKit(title: "原始交易")
                .padding(.horizontal, 16)

              /// 原始交易（添加导航功能）
              Button(action: {
                hapticManager.trigger(.selection)
                pathManager.path.append(
                  NavigationDestination.transactionDetailView(originalTransaction.id))
              }) {
                HStack(spacing: 12) {
                  // 分类图标
                  if let categoryId = originalTransaction.transactionCategoryId {
                    let (_, categoryIcon) = viewModel.getCategoryInfo(
                      from: categoryId,
                      transactionType: originalTransaction.transactionType,
                      categories: dataManager.mainCategories,
                      subCategories: dataManager.subCategories
                    )
                    IconView(
                      viewModel: IconViewVM(
                        icon: categoryIcon,
                        size: 40,
                        fontSize: 20,
                        backgroundColor: Color.clear,
                        cornerRadius: 12
                      ))
                  } else {
                    IconView(
                      viewModel: IconViewVM(
                        icon: IconType.emoji("❓"),
                        size: 40,
                        fontSize: 20,
                        backgroundColor: Color.clear,
                        cornerRadius: 12
                      ))
                  }

                  VStack(alignment: .leading, spacing: 4) {
                    // 显示原始交易的类别名称
                    if let categoryId = originalTransaction.transactionCategoryId {
                      let (categoryName, _) = viewModel.getCategoryInfo(
                        from: categoryId,
                        transactionType: originalTransaction.transactionType,
                        categories: dataManager.mainCategories,
                        subCategories: dataManager.subCategories
                      )
                      Text(categoryName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.cBlack)
                    } else {
                      Text("未知类别")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.cBlack)
                    }
                    Text(
                      DateFormattingHelper.shared.smartFormat(
                        date: originalTransaction.transactionDate)
                    )
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.cBlack.opacity(0.6))
                  }
                  Spacer()
                  VStack(alignment: .trailing, spacing: 4) {
                    Text(
                      "\(originalTransaction.symbol)\(NumberFormatService.shared.formatAmount(originalTransaction.transactionAmount))"
                    )
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.cBlack)

                    // 显示支出卡片信息
                    if let fromCardId = originalTransaction.fromCardId,
                      let fromCard = dataManager.cards.first(where: { $0.id == fromCardId })
                    {
                      HStack(spacing: 4) {
                        if let imageData = fromCard.bankLogo,
                          let uiImage = UIImage(data: imageData)
                        {
                          Image(uiImage: uiImage)
                            .resizable()
                            .frame(width: 16, height: 16)
                            .cornerRadius(4)
                        } else {
                          Rectangle()
                            .frame(width: 16, height: 16)
                            .foregroundColor(.cAccentBlue.opacity(0.3))
                            .cornerRadius(4)
                        }
                        Text(fromCard.name)
                          .font(.system(size: 13))
                          .foregroundColor(.cBlack.opacity(0.4))
                      }
                    }
                  }
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
              }
              .buttonStyle(PlainButtonStyle())
            }
            Spacer(minLength: 88)
          }
        }

        .background(Color.cWhite)
        .clipShape(
          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
        )
        .edgesIgnoringSafeArea(.bottom)
      }
      actionButtonSection

      // 状态信息区域（加载状态和错误信息）
      statusSection
    }
    .background(
      DottedGridBackground()
        .ignoresSafeArea(.all)  // 让背景延伸到状态栏区域
    )
    .floatingSheet(
      isPresented: $viewModel.showTimeSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectTimeSheet(
        selectedDate: Binding(
          get: { viewModel.editingData?.date ?? Date() },
          set: { viewModel.editingData?.date = $0 }
        ),
        onConfirm: {
          hapticManager.trigger(.selection)
          viewModel.showTimeSheet = false
        },
        onCancel: {
          hapticManager.trigger(.selection)
          viewModel.showTimeSheet = false
        }
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.numericKeypadText,
        onSave: {
          hapticManager.trigger(.selection)
          viewModel.saveNumericInput()
        },
        allowNegative: transaction.transactionType == .createCard
          || transaction.transactionType == .adjustCard,
        maxDecimalPlaces: 2
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showCategorySheet,
      config: SheetBase(
        maxDetent: .fraction(0.55), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectCategorySheet(
        title: "选择类别",
        transactionType: transaction.transactionType,
        selectedCategoryId: Binding(
          get: {
            return viewModel.editingData?.categoryId
          },
          set: { _ in }
        ),
        categories: dataManager.mainCategories,
        subCategories: dataManager.subCategories,
        onCategorySelected: { categoryId in
          hapticManager.trigger(.selection)
          viewModel.editingData?.categoryId = categoryId
          viewModel.showCategorySheet = false
        },
        onCancel: {
          hapticManager.trigger(.selection)
          viewModel.showCategorySheet = false
        }
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showCardSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectCardSheet(
        title: "选择卡片",
        selectedCardId: Binding(
          get: {
            switch transaction.transactionType {
            case .expense:
              return viewModel.editingData?.fromCardId
            case .income, .refund:
              return viewModel.editingData?.toCardId
            case .transfer:
              return viewModel.currentEditingCardType == .from
                ? viewModel.editingData?.fromCardId : viewModel.editingData?.toCardId
            case .createCard:
              return viewModel.editingData?.toCardId
            case .adjustCard:
              return viewModel.editingData?.fromCardId ?? viewModel.editingData?.toCardId
            }
          },
          set: { _ in }
        ),
        cards: dataManager.cards,
        onCardSelected: { cardId in
          hapticManager.trigger(.selection)
          viewModel.saveCardSelection(cardId: cardId)
        },
        onCancel: {
          hapticManager.trigger(.selection)
          viewModel.showCardSheet = false
        }
      )
    }

    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)

    .animation(.easeInOut(duration: 0.3), value: viewModel.isEditing)
    .alert("删除交易", isPresented: $viewModel.showDeleteAlert) {
      Button("取消", role: .cancel) {
        hapticManager.trigger(.selection)
      }
      Button("删除", role: .destructive) {
        hapticManager.trigger(.selection)
        handleDeleteAction()
      }
    } message: {
      Text("确定要删除这笔交易吗？此操作不可撤销。")
    }
    .alert("删除失败", isPresented: $viewModel.showDeleteErrorAlert) {
      Button("确定", role: .cancel) {
        hapticManager.trigger(.selection)
      }
    } message: {
      Text(viewModel.deleteErrorMessage)
    }
  }

  // MARK: - 状态信息区域
  /// 状态信息区域（错误信息）
  private var statusSection: some View {
    Group {
      // 显示错误信息
      if let errorMessage = viewModel.errorMessage {
        Text(errorMessage)
          .font(.system(size: 14))
          .foregroundColor(.red)
          .padding()
      }
    }
  }

  // MARK: - 私有方法

  /// 处理退款操作
  private func handleRefundAction() {
    if let destination = viewModel.handleRefundAction() {
      pathManager.path.append(destination)
    }
  }

  /// 获取当前显示的数据（编辑状态下使用编辑数据，否则使用原始数据）
  private func getCurrentDisplayData() -> TransactionDetailViewModel.TransactionDetailData? {
    return viewModel.currentDisplayData
  }

  /// 获取过滤后的类别列表
  private func getFilteredCategories() -> [TransactionMainCategoryModel] {
    return viewModel.filteredCategories
  }

  /// 处理保存操作
  private func handleSaveAction() {
    if viewModel.handleSaveAction(modelContext: modelContext) {
      // 保存成功，触觉反馈
      hapticManager.trigger(.selection)
    }
  }

  /// 处理删除操作
  private func handleDeleteAction() {
    if viewModel.handleDeleteAction(modelContext: modelContext) {
      // 删除成功后返回上一页
      dismiss()
    }
  }

}

// MARK: - 操作按钮组件

/// 操作按钮组件（支持滑动动画）
private struct ActionButtonsView: View {
  @Binding var isEditing: Bool
  let transactionType: TransactionType
  let canRefund: Bool
  let onRefund: () -> Void
  let onEdit: () -> Void
  let onCancel: () -> Void
  let onSave: () -> Void

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    HStack(spacing: 32) {
      Spacer()

      if !isEditing {
        // 默认状态：根据交易类型显示不同按钮
        HStack(spacing: 32) {
          // 只有支出交易显示退款按钮
          if transactionType == .expense {
            Button(action: {
              hapticManager.trigger(.selection)
              onRefund()
            }) {
              Text("退款")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(canRefund ? Color.cAccentRed : Color.cBlack.opacity(0.4))
                .padding(.horizontal, 36)
                .frame(height: 40)
                .background(
                  canRefund ? Color.cAccentRed.opacity(0.12) : Color.cBlack.opacity(0.05)
                )
                .cornerRadius(20)
            }
            .disabled(!canRefund)
          }

          Button(action: {
            hapticManager.trigger(.selection)
            withAnimation(.easeInOut(duration: 0.3)) {
              onEdit()
            }
          }) {
            Text("编辑")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.accentColor)
              .padding(.horizontal, 36)
              .frame(height: 40)
              .background(Color.accentColor.opacity(0.12))
              .cornerRadius(20)
          }
        }
        .transition(
          .asymmetric(
            insertion: .move(edge: .leading).combined(with: .opacity),
            removal: .move(edge: .leading).combined(with: .opacity)
          ))
      } else {
        // 编辑状态：取消 + 保存按钮
        HStack(spacing: 32) {
          Button(action: {
            hapticManager.trigger(.selection)
            withAnimation(.easeInOut(duration: 0.3)) {
              onCancel()
            }
          }) {
            Text("取消")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.6))
              .padding(.horizontal, 36)
              .frame(height: 40)
              .background(Color.cBlack.opacity(0.08))
              .cornerRadius(20)
          }

          Button(action: {
            hapticManager.trigger(.selection)
            withAnimation(.easeInOut(duration: 0.3)) {
              onSave()
            }
          }) {
            Text("保存")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.accentColor)
              .padding(.horizontal, 36)
              .frame(height: 40)
              .background(Color.accentColor.opacity(0.12))
              .cornerRadius(20)
          }
        }
        .transition(
          .asymmetric(
            insertion: .move(edge: .trailing).combined(with: .opacity),
            removal: .move(edge: .trailing).combined(with: .opacity)
          ))
      }

      Spacer()
    }
    .animation(.easeInOut(duration: 0.3), value: isEditing)
  }
}

// MARK: - 卡片显示组件

/// 单卡片显示组件
private struct SingleCardView: View {
  let card: CardModel?
  let cardType: String
  let isEditing: Bool
  let onEditCard: (() -> Void)?

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  init(
    card: CardModel?, cardType: String, isEditing: Bool, onEditCard: (() -> Void)? = nil,
  ) {
    self.card = card
    self.cardType = cardType
    self.isEditing = isEditing
    self.onEditCard = onEditCard
  }

  var body: some View {
    HStack(spacing: 12) {
      // 卡片图标
      if let card = card,
        let imageData = card.bankLogo,
        let uiImage = UIImage(data: imageData)
      {
        Image(uiImage: uiImage)
          .resizable()
          .frame(width: 40, height: 40)
          .cornerRadius(12)
      } else {
        IconView(
          viewModel: IconViewVM(
            icon: IconType.emoji("❓"),
            size: 40,
            fontSize: 20,
            backgroundColor: Color.cAccentBlue.opacity(0.1),
            cornerRadius: 12
          ))
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(card?.name ?? "未知卡片")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
        Text(cardType)
          .font(.system(size: 13, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }
      Spacer()
      if isEditing {
        ActionButton(
          viewModel: ActionButtonVM.customColor(
            title: "编辑卡片",
            action: {
              hapticManager.trigger(.selection)
              onEditCard?()
            },
            textColor: Color.cAccentBlue,
            strokeColor: Color.cAccentBlue.opacity(0.08)
          )
        )
        .transition(.move(edge: .trailing).combined(with: .opacity))
      }
    }
    .padding(.horizontal, 24)
    .padding(.vertical, 12)
  }
}

/// 转账双卡片显示组件
private struct TransferCardsView: View {
  let fromCard: CardModel?
  let toCard: CardModel?
  let isEditing: Bool
  let onEditFromCard: (() -> Void)?
  let onEditToCard: (() -> Void)?

  init(
    fromCard: CardModel?, toCard: CardModel?, isEditing: Bool,
    onEditFromCard: (() -> Void)? = nil,
    onEditToCard: (() -> Void)? = nil,
  ) {
    self.fromCard = fromCard
    self.toCard = toCard
    self.isEditing = isEditing
    self.onEditFromCard = onEditFromCard
    self.onEditToCard = onEditToCard
  }

  var body: some View {
    VStack(spacing: 0) {
      // 支出卡片
      SingleCardView(
        card: fromCard,
        cardType: "支出卡片",
        isEditing: isEditing,
        onEditCard: onEditFromCard
      )

      // 收入卡片
      SingleCardView(
        card: toCard,
        cardType: "收入卡片",
        isEditing: isEditing,
        onEditCard: onEditToCard
      )
    }
  }
}

// MARK: - Category Row Button

private struct CategoryRowButton: View {
  let category: TransactionMainCategoryModel
  let onTap: () -> Void

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    Button(action: {
      hapticManager.trigger(.selection)
      onTap()
    }) {
      HStack {
        IconView(
          viewModel: IconViewVM(
            icon: category.icon,
            size: 20,
            fontSize: 16,
            backgroundColor: Color.clear,
            cornerRadius: 0
          ))

        Text(category.name)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .background(Color.cWhite)
      .cornerRadius(12)
    }
  }
}

//#Preview {
//    TransactionDetailView()
//}
