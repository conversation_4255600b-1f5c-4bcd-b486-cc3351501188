//
//  DividerKit.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

// MARK: - 分割线样式枚举

/// 分割线样式类型
enum DividerStyle {
  case solid  // 实线
  case dashed  // 虚线
}

// MARK: - 分割线组件

/// 通用分割线组件
///
/// 提供统一的分割线样式，支持实线和虚线两种模式，
/// 可自定义颜色、高度和虚线模式。
///
/// ## 使用示例
/// ```swift
/// // 默认实线
/// DividerKit()
///
/// // 虚线
/// DividerKit.dashed()
///
/// // 自定义颜色和高度
/// DividerKit.solid(color: .blue, height: 2)
/// ```
struct DividerKit: View {
  /// 样式
  let style: DividerStyle
  /// 颜色
  let color: Color
  /// 高度（线宽）
  let height: CGFloat
  /// 虚线模式 [dash长度, gap长度]
  let dashPattern: [CGFloat]

  /// 初始化分割线
  /// - Parameters:
  ///   - style: 分割线样式，默认为实线
  ///   - color: 分割线颜色，默认为蓝色 20% 透明度
  ///   - height: 分割线高度，默认为 1pt
  ///   - dashPattern: 虚线模式，默认为 [5]
  init(
    style: DividerStyle = .solid,
    color: Color = Color.cAccentBlue.opacity(0.2),
    height: CGFloat = 1,
    dashPattern: [CGFloat] = [5]
  ) {
    self.style = style
    self.color = color
    self.height = height
    self.dashPattern = dashPattern
  }

  var body: some View {
    GeometryReader { geometry in
      Path { path in
        path.move(to: CGPoint(x: 0, y: 0))
        path.addLine(to: CGPoint(x: geometry.size.width, y: 0))
      }
      .stroke(
        color,
        style: style == .dashed
          ? SwiftUI.StrokeStyle(lineWidth: height, dash: dashPattern)
          : SwiftUI.StrokeStyle(lineWidth: height)
      )
    }
    .frame(height: height)
  }
}

// MARK: - 便利初始化扩展

extension DividerKit {
  /// 创建默认实线分割线
  static var `default`: DividerKit {
    DividerKit()
  }
  
  /// 创建虚线分割线
  static func dashed(
    color: Color = Color.cAccentBlue.opacity(0.2),
    height: CGFloat = 1,
    dashPattern: [CGFloat] = [5]
  ) -> DividerKit {
    return DividerKit(
      style: .dashed,
      color: color,
      height: height,
      dashPattern: dashPattern
    )
  }

  /// 创建实线分割线
  /// - Parameters:
  ///   - color: 分割线颜色
  ///   - height: 分割线高度
  /// - Returns: 实线分割线实例
  static func solid(
    color: Color = Color.cAccentBlue.opacity(0.2),
    height: CGFloat = 1
  ) -> DividerKit {
    return DividerKit(
      style: .solid,
      color: color,
      height: height
    )
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 20) {
    Text("实线分割线")
    DividerKit.solid()

    Text("虚线分割线")
    DividerKit.dashed()

    Text("自定义颜色虚线")
    DividerKit.dashed(color: .red)

    Text("自定义高度实线")
    DividerKit.solid(color: .blue, height: 2)

    Text("自定义虚线模式")
    DividerKit.dashed(
      color: .green,
      height: 2,
      dashPattern: [10, 5]
    )
  }
  .padding()
}
